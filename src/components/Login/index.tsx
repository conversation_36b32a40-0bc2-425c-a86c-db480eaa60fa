import { <PERSON>, EyeOff } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, TouchableOpacity } from 'react-native';
import { useDeviceSerial } from '../../hooks/useDeviceSerial';
import { Container, EyeButton, ForgotPasswordText, FormContainer, InputContainer, InputLabel, InputWrapper, LoginButton, LoginButtonText, Logo, LogoContainer, LogoText, PasswordInput, RememberContainer, RememberRow, Subtitle, TextInput, WelcomeTitle } from './styles';

interface LoginScreenProps {
  onLoginSuccess: (credentials: {
    email: string;
    password: string;
    totem_serial: string;
  }) => void;
  isLoading: boolean;
}

export default function LoginScreen({onLoginSuccess, isLoading}: LoginScreenProps) {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('franquia123');
  const [showPassword, setShowPassword] = useState(false);

  const {serialNumber} = useDeviceSerial();
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erro', 'Por favor, preencha todos os campos');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Erro', 'Por favor, informe um e-mail válido');
      return;
    }

    if (!serialNumber) {
      Alert.alert('Erro', 'Não foi possível obter o serial do dispositivo.');
      return;
    }

    try {
      onLoginSuccess({
        email,
        password,
        totem_serial: serialNumber,
      });
    } catch (error) {
      Alert.alert('Erro', 'Falha no login. Tente novamente.');
    }
  };

  const handleForgotPassword = () => {
    Alert.alert('Esqueceu a senha?', 'Funcionalidade em desenvolvimento');
  };

  return (
    <Container>
      <LogoContainer>
        <Logo>
          <LogoText>🍕</LogoText>
        </Logo>
        <WelcomeTitle>Bem indo ao Itaú Totem</WelcomeTitle>
        <Subtitle>Seu Totem de auto atendimento</Subtitle>
      </LogoContainer>

      <FormContainer>
        <InputContainer>
          <InputLabel>Email</InputLabel>
          <TextInput
            placeholder="Digite seu email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
          />
        </InputContainer>

        <InputContainer>
          <InputLabel>Password</InputLabel>
          <InputWrapper>
            <PasswordInput
              placeholder="Digite sua senha"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
            />
            <EyeButton onPress={() => setShowPassword(!showPassword)}>
              {showPassword ? (
                <EyeOff size={24} color="#666666" />
              ) : (
                <Eye size={24} color="#666666" />
              )}
            </EyeButton>
          </InputWrapper>
        </InputContainer>

        <RememberContainer>
          <RememberRow />
          <TouchableOpacity onPress={handleForgotPassword}>
            <ForgotPasswordText>Forgot Password?</ForgotPasswordText>
          </TouchableOpacity>
        </RememberContainer>

        <LoginButton onPress={handleLogin} disabled={isLoading}>
          <LoginButtonText>{isLoading ? 'Entrando...' : 'Login'}</LoginButtonText>
        </LoginButton>
      </FormContainer>
    </Container>
  );
}
