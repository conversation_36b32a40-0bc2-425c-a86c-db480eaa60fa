import { <PERSON>ert<PERSON><PERSON><PERSON>, ArrowLeft, CheckCircle2, <PERSON><PERSON><PERSON>, Printer } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
} from 'react-native';
import { aditumSdkService, PaymentType } from '../../../services/aditum/AditumSdk';
import { convertToReais } from '../../../utils/convertToReais';
import { formatCurrency } from '../../../utils/formatCurrency';
import { ActionsContainer, AmountContainer, AmountLabel, AmountValue, BackButton, Container, Content, ContentContainer, DetailLabel, DetailRow, DetailValue, Header, HeaderSpacer, HeaderTitle, LoadingContainer, LoadingText, PrimaryButton, PrimaryButtonText, ProcessingText, SecondaryButton, SecondaryButtonText, StatusContainer, Subtitle, Title, TransactionDetails } from './styles';

interface CardPaymentScreenProps {
  onBack: () => void;
  onCancel: () => void;
  onPaymentSuccess: (paymentMethod: PaymentMethod) => Promise<void> | void;
  amount: number;
  installments_qty: number;
  transactionId?: string;
}

type PaymentMethod = 'CARD';
type PaymentStatus = 'initializing' | 'waiting' | 'processing' | 'success' | 'error';

interface PaymentResponse {
  charge: {
    nsu: string;
    chargeStatus: string;
    amount: number;
    cardNumber: string;
  };
}

export function CardPaymentScreen({
  onBack,
  onCancel,
  onPaymentSuccess,
  amount,
  installments_qty,
  transactionId,
}: CardPaymentScreenProps) {
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>('initializing');
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    initializePayment();
  }, []);

  useEffect(() => {
    if (paymentStatus === 'success') {
      onPaymentSuccess('CARD');
    }
  }, [paymentStatus]);

  const initializePayment = async () => {
    try {
      setIsLoading(true);

      const status = aditumSdkService.getServiceStatus();
      console.log('Status do SDK:', status);

      if (!status.isInitialized) {
        const response = await aditumSdkService.fullInitialization(
          'Pagway TEF PROD',
          '1.0.0',
          '436596309',
        );

        if (!response.initialized) {
          throw new Error('Falha na inicialização do SDK');
        }
      }

      await handleProcessPayment();
    } catch (error) {
      console.error('Erro na inicialização:', error);
      setErrorMessage('Erro ao inicializar sistema de pagamento');
      setPaymentStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProcessPayment = async () => {
    try {
      setIsLoading(true);
      setPaymentStatus('waiting');

      await aditumSdkService.ensureServiceReady(
        'Pagway TEF PROD',
        '1.0.0',
        '436596309',
      );

      const response = await aditumSdkService.processPayment({
        amount: amount,
        paymentType: installments_qty === 0 ? PaymentType.Debit : PaymentType.Credit,
        installment: installments_qty,
        merchantChargeId: transactionId || `ORDER_${Date.now()}`,
      });

      setPaymentResponse(response);
      setPaymentStatus('processing');

      console.log(JSON.stringify(response, null, 2));

      if (response.charge && response.charge.chargeStatus === 'Authorized' && response.isApproved) {
        const confirmed = await aditumSdkService.confirmTransaction(response.charge.nsu);

        if (confirmed) {
          setPaymentStatus('success');
        } else {
          throw new Error('Falha na confirmação da transação');
        }
      } else {
        if (response.charge) {
          throw new Error(`Transação recusada, status: ${response.charge.chargeStatus}, code: ${response.charge.authorizationResponseCode}`);
        } else if (response.errors && response.errors.some(error => error.message === 'OPERAÇÃO CANCELADA')) {
          throw new Error('Transação cancelada pelo portador');
        } else {
          throw new Error('Transação recusada');
        }
      }

    } catch (error: any) {
      console.log('Erro no pagamento', error);
      if (error.toString().includes('SERVICE_NOT_AVAILABLE') ||
          error.toString().includes('service is not connected')) {
        setErrorMessage('Conexão com o terminal perdida. Tentando reconectar...');
        await handleRetryWithReconnection();
      } else {
        setErrorMessage(`Erro ao processar pagamento - ${error}`);
        setPaymentStatus('error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetryWithReconnection = async () => {
    try {
      setIsLoading(true);
      setPaymentStatus('initializing');
      setErrorMessage('Reconectando terminal...');

      await aditumSdkService.ensureServiceReady(
        'Pagway TEF PROD',
        '1.0.0',
        '436596309',
      );

      await handleProcessPayment();
    } catch (error) {
      console.error('Erro na reconexão:', error);
      setErrorMessage(`Falha ao reconectar: ${error}`);
      setPaymentStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelTransaction = async () => {
    if (!paymentResponse?.charge.nsu) {return;}

    try {
      setIsLoading(true);

      await aditumSdkService.ensureServiceReady(
        'Pagway TEF PROD',
        '1.0.0',
        '436596309',
      );

      const canceled = await aditumSdkService.cancelTransaction(
        paymentResponse.charge.nsu,
        false
      );

      if (canceled) {
        Alert.alert('Sucesso', 'Transação cancelada com sucesso!');
        onBack();
      } else {
        Alert.alert('Erro', 'Falha ao cancelar transação');
      }
    } catch (error) {
      console.error('Erro ao cancelar:', error);
      Alert.alert('Erro', `Erro ao cancelar transação: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTryAgain = () => {
    setPaymentStatus('initializing');
    setPaymentResponse(null);
    setErrorMessage('');
    initializePayment();
  };

  const handleFinish = async () => {
    onBack();
  };

  const handleAbort = async () => {
    setIsSubmitting(true);
    await aditumSdkService.abortPayment().then(() => {
      console.log('Pagamento abortado');
      setPaymentStatus('processing');
      setIsLoading(false);
      setIsSubmitting(false);
    });
  };

  const handlePrintReceipt = async () => {
    try {
      Alert.alert('Sucesso', 'Comprovante impresso com sucesso!');
    } catch (error) {
      console.error('Erro ao imprimir comprovante:', error);
      Alert.alert('Erro', 'Falha ao imprimir comprovante');
    }
  };

  const renderStatusIcon = () => {
    switch (paymentStatus) {
      case 'initializing':
        return <ActivityIndicator size={80} color="#2563eb" />;
      case 'waiting':
        return <CreditCard size={80} color="#2563eb" />;
      case 'processing':
        return <ActivityIndicator size={80} color="#2563eb" />;
      case 'success':
        return <CheckCircle2 size={80} color="#16a34a" />;
      case 'error':
        return <AlertCircle size={80} color="#dc2626" />;
      default:
        return <CreditCard size={80} color="#2563eb" />;
    }
  };

  const renderContent = () => {
    switch (paymentStatus) {
      case 'initializing':
        return (
          <ContentContainer>
            <Title>Inicializando Sistema</Title>
            <Subtitle>Preparando terminal de pagamento...</Subtitle>
            {errorMessage.includes('Reconectando') && (
              <ProcessingText>{errorMessage}</ProcessingText>
            )}
          </ContentContainer>
        );

      case 'waiting':
        return (
          <ContentContainer>
            <Title>Insira ou Aproxime o Cartão</Title>
            <AmountContainer>
              <AmountLabel>Valor a pagar</AmountLabel>
              <AmountValue>
                {formatCurrency(convertToReais(amount))}
              </AmountValue>
            </AmountContainer>
            <Subtitle>
              Aguardando cartão no terminal...
            </Subtitle>
          </ContentContainer>
        );

      case 'processing':
        return (
          <ContentContainer>
            <Title>Processando Pagamento</Title>
            <Subtitle>
              Aguarde, não remova o cartão do terminal
            </Subtitle>
            <ProcessingText>
              Isso pode levar alguns segundos...
            </ProcessingText>
          </ContentContainer>
        );

      case 'success':
        return (
          <ContentContainer>
            <Title color="#16a34a">
              Pagamento Aprovado!
            </Title>
            <Subtitle>
              Transação realizada com sucesso
            </Subtitle>

            {paymentResponse && (
              <TransactionDetails>
                <DetailRow>
                  <DetailLabel>Valor pago:</DetailLabel>
                  <DetailValue>
                    {formatCurrency(convertToReais(paymentResponse.charge.amount))}
                  </DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>NSU:</DetailLabel>
                  <DetailValue>
                    {paymentResponse.charge.nsu}
                  </DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>Cartão:</DetailLabel>
                  <DetailValue>
                    {paymentResponse.charge.cardNumber || 'N/A'}
                  </DetailValue>
                </DetailRow>
                <DetailRow>
                  <DetailLabel>Data:</DetailLabel>
                  <DetailValue>
                    {new Date().toLocaleString('pt-BR')}
                  </DetailValue>
                </DetailRow>
              </TransactionDetails>
            )}
          </ContentContainer>
        );

      case 'error':
        return (
          <ContentContainer>
            <Title color="#dc2626">
              Pagamento Negado
            </Title>
            <Subtitle>
              {errorMessage || 'Não foi possível processar o pagamento'}
            </Subtitle>
            {errorMessage.includes('reconectar') && (
              <ProcessingText>
                Verifique se o terminal está conectado e funcionando
              </ProcessingText>
            )}
          </ContentContainer>
        );

      default:
        return null;
    }
  };

  const renderActions = () => {
    switch (paymentStatus) {
      case 'success':
        return (
          <ActionsContainer>
            <PrimaryButton onPress={handleFinish}>
              <PrimaryButtonText>Finalizar</PrimaryButtonText>
            </PrimaryButton>

            <SecondaryButton onPress={handlePrintReceipt}>
              <Printer size={24} color="#6b7280" />
              <SecondaryButtonText>Imprimir Comprovante</SecondaryButtonText>
            </SecondaryButton>
          </ActionsContainer>
        );

      case 'error':
        return (
          <ActionsContainer>
            <PrimaryButton onPress={handleTryAgain}>
              <PrimaryButtonText>Tentar Novamente</PrimaryButtonText>
            </PrimaryButton>

            <SecondaryButton onPress={onCancel}>
              <SecondaryButtonText>Cancelar</SecondaryButtonText>
            </SecondaryButton>
          </ActionsContainer>
        );

      case 'waiting':
        return (
          <ActionsContainer>
            <SecondaryButton onPress={handleAbort} disabled={isSubmitting}>
              {
                isSubmitting ? (
                  <SecondaryButtonText>Cancelando...</SecondaryButtonText>
                ) : (
                  <SecondaryButtonText>Cancelar</SecondaryButtonText>
                )
              }
            </SecondaryButton>
          </ActionsContainer>
        );

      default:
        return null;
    }
  };

  return (
    <Container>
      {/* Header */}
      <Header>
        {paymentStatus !== 'processing' && (
          <BackButton onPress={() => {}}>
            {/* <ArrowLeft size={24} color="#374151" /> */}
          </BackButton>
        )}
        <HeaderTitle>Pagamento com Cartão</HeaderTitle>
        <HeaderSpacer />
      </Header>

      {/* Content */}
      <Content>
        <StatusContainer>
          {renderStatusIcon()}
        </StatusContainer>

        {renderContent()}

        {isLoading && paymentStatus !== 'processing' && (
          <LoadingContainer>
            <ActivityIndicator size={32} color="#2563eb" />
            <LoadingText>Processando...</LoadingText>
          </LoadingContainer>
        )}
      </Content>

      {/* Actions */}
      {renderActions()}
    </Container>
  );
}
