export type OrderStatus = 'PENDING' | 'WAITING_PRINT' | 'COMPLETED' | 'CANCELLED';

export interface ICreateOrderRequest {
  transactionId: string
  products: {
    id: string
    quantity: number
  }[]
  status: OrderStatus
}

export interface IOrderProduct {
  id: string
  name: string
  description: string
}

interface IOrderItem {
  id: string
  created_at: Date | null
  updated_at: Date | null
  quantity: number
  unit_price: string
  total_price: string
  order_id: string
  product_id: string
  Product: IOrderProduct
}


export interface IOrder {
  id: string
  created_at: Date | null
  updated_at: Date | null
  status: string | null
  transaction_id: string
  totem_id: string
  OrderItem: IOrderItem[]
}
