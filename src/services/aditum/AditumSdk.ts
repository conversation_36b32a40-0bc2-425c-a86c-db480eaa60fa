import { NativeModules } from 'react-native';
interface IMerchantData {
  allowPartialCancel: boolean;
  contaclessEnabled: boolean;
  document: string;
  fantasyName: string;
  id: string;
  incrementalPreAuthorizationEnabled: boolean;
  paymentScheme: {
    acquirer: string;
    name: string;
    paymentType: 'Credit' | 'Debit' | 'Voucher' | 'Wallet' | 'Pix';
    paymentTypeRestriction?: {
      installmentType: 'None' | 'Merchant' | 'Issuer';
      label: string;
      maxInstallmentAmount: number;
      maxInstallmentNumber: number;
      maxTransactionAmount: number;
      minInstallmentAmount: number;
      minInstallmentNumber: number;
      minTransactionAmount: number;
      paymentType: 'Credit' | 'Debit' | 'Voucher' | 'Wallet' | 'Pix';
    }[];
    scheme: string;
  }[];
  preAuthorizationEnabled: boolean;
  preAuthorizationInstallmentEnabled: boolean;
  typedPanEnabled: boolean;
}

export interface InitResponse {
  initialized: boolean;
}

export interface IPaymentResponse {
  errors?: any[];
  charge: {
    acquirer: string;
    aid: string;
    amount: number;
    authorizationCode: string;
    authorizationResponseCode: string;
    brand: string;
    cardNumber: string;
    cardholderName: string;
    cardholderReceipt: string[];
    chargeStatus: string;
    creationDateTime: string;
    currency: number;
    installmentNumber: number;
    installmentType: string;
    isApproved: boolean;
    isCanceled: boolean;
    merchantChargeId: string;
    merchantReceipt: string[];
    nsu: string;
    origin: string;
    paymentType: string;
    transactionId: string;
  };
  isApproved: boolean;
}

export interface PaymentRequest {
  amount: number;
  installment: number;
  paymentType: PaymentType;
  merchantChargeId?: string;
}

export enum PaymentType {
  Undefined = 'Undefined',
  Credit = 'Credit',
  Debit = 'Debit',
  Voucher = 'Voucher',
  Pix = 'Pix',
  Wallet = 'Wallet'
}

interface AditumSdkModuleInterface {
  startAditumSdkService(): Promise<string>;
  initAditumSdk(
    applicationName: string,
    applicationVersion: string,
    activationCode?: string,
  ): Promise<InitResponse>;
  getMerchantData(): Promise<string>;
  pay(amount: number, paymentType: PaymentType, installments: number, merchantChargeId?: string): Promise<string>;
  abort(): Promise<string>;
  confirm(nsu: string): Promise<boolean>;
  cancel(nsu: string, isReversal: boolean): Promise<boolean>;
  deactivate(): Promise<boolean>;
}

const { AditumSdkModule } = NativeModules as { AditumSdkModule: AditumSdkModuleInterface };

export class AditumSdkService {
  private static instance: AditumSdkService;
  private isServiceStarted = false;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): AditumSdkService {
    if (!AditumSdkService.instance) {
      AditumSdkService.instance = new AditumSdkService();
    }
    return AditumSdkService.instance;
  }

  /**
   * Inicia o serviço Aditum SDK
   */
  public async startService(): Promise<void> {
    try {
      if (this.isServiceStarted) {
        console.log('Aditum SDK service already started');
        return;
      }

      const result = await AditumSdkModule.startAditumSdkService();
      this.isServiceStarted = true;
      console.log('Aditum SDK service started:', result);
    } catch (error) {
      console.error('Error starting Aditum SDK service:', error);
      throw new Error(`Failed to start service: ${error}`);
    }
  }

  /**
   * Inicializa o SDK Aditum
   */
  public async initializeSdk(
    applicationName: string,
    applicationVersion: string = '1.0.0',
    activationCode?: string,
  ): Promise<InitResponse> {
    try {
      if (!this.isServiceStarted) {
        throw new Error('Service must be started before initialization');
      }

      const response = await AditumSdkModule.initAditumSdk(
        applicationName,
        applicationVersion,
        activationCode,
      );

      this.isInitialized = response.initialized;
      return response;
    } catch (error) {
      console.error('Error initializing Aditum SDK:', error);
      throw new Error(`Failed to initialize SDK: ${error}`);
    }
  }

  /**
   * Obtém os dados do merchant
   */
  public async getMerchantData(): Promise<IMerchantData> {
    try {
      const merchantDataJson = await AditumSdkModule.getMerchantData();
      return JSON.parse(merchantDataJson) as IMerchantData;
    } catch (error) {
      console.error('Error getting merchant data:', error);
      throw new Error(`Failed to get merchant data: ${error}`);
    }
  }

  /**
   * Realiza um pagamento
   */
  public async processPayment(paymentRequest: PaymentRequest): Promise<IPaymentResponse> {
    try {
      const { amount, paymentType, installment, merchantChargeId } = paymentRequest;

      if (!amount || amount <= 0) {
        throw new Error('Amount must be greater than 0');
      }

      const paymentResponseJson = await AditumSdkModule.pay(amount, paymentType, installment, merchantChargeId);
      return JSON.parse(paymentResponseJson) as IPaymentResponse;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw new Error(`Failed to process payment: ${error}`);
    }
  }

  /**
   * Aborta uma tentativa de pagamento
   */
  public async abortPayment(): Promise<void> {
    try {
      await AditumSdkModule.abort();
    } catch (error) {
      console.error('Error aborting payment:', error);
      throw new Error(`Failed to abort payment: ${error}`);
    }
  }

  /**
   * Confirma uma transação
   */
  public async confirmTransaction(nsu: string): Promise<boolean> {
    try {
      if (!nsu) {
        throw new Error('NSU is required for confirmation');
      }

      return await AditumSdkModule.confirm(nsu);
    } catch (error) {
      console.error('Error confirming transaction:', error);
      throw new Error(`Failed to confirm transaction: ${error}`);
    }
  }

  /**
   * Cancela uma transação
   */
  public async cancelTransaction(nsu: string, isReversal: boolean = false): Promise<boolean> {
    try {
      if (!nsu) {
        throw new Error('NSU is required for cancellation');
      }

      return await AditumSdkModule.cancel(nsu, isReversal);
    } catch (error) {
      console.error('Error canceling transaction:', error);
      throw new Error(`Failed to cancel transaction: ${error}`);
    }
  }

  /**
   * Desativa o SDK
   */
  public async deactivate(): Promise<boolean> {
    try {
      const result = await AditumSdkModule.deactivate();
      if (result) {
        this.isInitialized = false;
        this.isServiceStarted = false;
      }
      return result;
    } catch (error) {
      console.error('Error deactivating SDK:', error);
      throw new Error(`Failed to deactivate SDK: ${error}`);
    }
  }

  /**
   * Retorna o status do serviço
   */
  public getServiceStatus(): { isServiceStarted: boolean; isInitialized: boolean } {
    return {
      isServiceStarted: this.isServiceStarted,
      isInitialized: this.isInitialized,
    };
  }

  /**
   * Método utilitário para inicializar completamente o SDK
   */
  public async fullInitialization(
    applicationName: string,
    applicationVersion: string = '1.0.0',
    activationCode?: string,
  ): Promise<InitResponse> {
    try {
      if (!this.isServiceStarted) {
        await this.startService();
      }

      await new Promise(resolve => setTimeout(resolve, 1000));

      return await this.initializeSdk(
        applicationName,
        applicationVersion,
        activationCode,
      );
    } catch (error) {
      console.error('Error during full initialization:', error);
      throw new Error(`Failed to complete full initialization: ${error}`);
    }
  }

  /**
   * Método para garantir que o serviço esteja conectado antes de operações críticas
   */
  public async ensureServiceReady(
    applicationName: string,
    applicationVersion: string = '1.0.0',
    activationCode?: string,
  ): Promise<void> {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        await this.getMerchantData();
        return;
      } catch (error) {
        retryCount++;
        console.log(`Tentativa ${retryCount} de reconectar o serviço...`);
        if (retryCount < maxRetries) {
          this.isServiceStarted = false;
          this.isInitialized = false;
          await this.fullInitialization(applicationName, applicationVersion, activationCode);
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
          throw new Error(`Falha ao reconectar o serviço após ${maxRetries} tentativas: ${error}`);
        }
      }
    }
  }
}

export const aditumSdkService = AditumSdkService.getInstance();
