import {
  IConsultResponse,
  IVehicleDebtsResponse,
  IVehicleDebtsSearchParams,
} from '../../types/VehiclesDebts';
import { api } from '../../utils/api';

class VehicleDebtsService {
  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  private async consultDebts(params: IVehicleDebtsSearchParams): Promise<IConsultResponse> {
    const response = await api.post('/vehicles-debts/consult', {
      state: params.state || 'DF',
      licensePlate: params.licensePlate,
      renavam: params.renavam,
      cpfCnpj: params.cpfCnpj,
    });

    return response.data;
  }

  private async getDebtsResult(debtsId: string): Promise<IVehicleDebtsResponse> {
    const maxRetries = 10;

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await api.get(`vehicles-debts/webhook/vehicles-debts/${debtsId}`);

        if (response.data.debts_data && Array.isArray(response.data.debts_data)) {
          return response.data;
        }

        if (response.data.message === 'Veículo não encontrado') {
          throw new Error(response.data.message);
        }

        throw new Error('Dados de débitos não encontrados');
      } catch (error: any) {
        console.error(error);
        if (i === maxRetries - 1) {throw error;}

        if (error.response?.data?.message === 'Veículo não encontrado') {
          throw new Error(error.response.data.message);
        }

        if (error.response?.status === 400) {
          await this.delay(3000);
          continue;
        }
        throw error;
      }
    }

    throw new Error('Máximo de tentativas excedido');
  }

  public async searchDebts(params: IVehicleDebtsSearchParams): Promise<IVehicleDebtsResponse> {
    if (!params.licensePlate && !params.renavam) {
      throw new Error('É necessário informar a placa ou RENAVAM do veículo');
    }

    const consultResult = await this.consultDebts(params);
    await this.delay(4000);
    return this.getDebtsResult(consultResult.transactionId.toString());
  }

  // public formatPlate = (plate: string) => plate.replace(/[^A-Z0-9]/g, '').toUpperCase();
  // public formatRenavam = (renavam: string) => renavam.replace(/\D/g, '');
}

export const vehicleDebtsService = new VehicleDebtsService();
