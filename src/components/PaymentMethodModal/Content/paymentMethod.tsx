import React from 'react';
import { CreditCard, Smartphone } from 'lucide-react-native';
import {
  PaymentMethodsContainer,
  PaymentMethodCard,
  PaymentMethodIcon,
  PaymentMethodInfo,
  PaymentMethodTitle,
  PaymentMethodSubtitle,
  CancelButton,
  CancelButtonText,
} from './styles';

export type PaymentMethod = 'PIX' | 'CARD';

interface PaymentMethodContentProps {
  onPaymentSelect: (method: PaymentMethod) => void;
  onCancel: () => void;
  showPix?: boolean;
  showCard?: boolean;
  pixLabel?: string;
  cardLabel?: string;
  pixSubtitle?: string;
  cardSubtitle?: string;
  cancelLabel?: string;
}

export function PaymentMethodContent({
  onPaymentSelect,
  onCancel,
  showPix = true,
  showCard = true,
  pixLabel = 'PIX',
  cardLabel = 'Cartão de Crédito/Débito',
  pixSubtitle = 'Pagamento instantâneo via QR Code',
  cardSubtitle = 'Parcelamento disponível',
  cancelLabel = 'Cancelar',
}: PaymentMethodContentProps) {
  return (
    <>
      <PaymentMethodsContainer>
        {showPix && (
          <PaymentMethodCard onPress={() => onPaymentSelect('PIX')}>
            <PaymentMethodIcon style={{ backgroundColor: '#E8F5E8' }}>
              <Smartphone size={48} color="#00BC7E" />
            </PaymentMethodIcon>
            <PaymentMethodInfo>
              <PaymentMethodTitle>{pixLabel}</PaymentMethodTitle>
              <PaymentMethodSubtitle>{pixSubtitle}</PaymentMethodSubtitle>
            </PaymentMethodInfo>
          </PaymentMethodCard>
        )}

        {showCard && (
          <PaymentMethodCard onPress={() => onPaymentSelect('CARD')}>
            <PaymentMethodIcon style={{ backgroundColor: '#E8F4FD' }}>
              <CreditCard size={48} color="#4A90E2" />
            </PaymentMethodIcon>
            <PaymentMethodInfo>
              <PaymentMethodTitle>{cardLabel}</PaymentMethodTitle>
              <PaymentMethodSubtitle>{cardSubtitle}</PaymentMethodSubtitle>
            </PaymentMethodInfo>
          </PaymentMethodCard>
        )}
      </PaymentMethodsContainer>

      <CancelButton onPress={onCancel}>
        <CancelButtonText>{cancelLabel}</CancelButtonText>
      </CancelButton>
    </>
  );
}
