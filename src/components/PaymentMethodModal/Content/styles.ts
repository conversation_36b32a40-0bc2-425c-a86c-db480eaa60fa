import styled from 'styled-components/native';

// Payment Method Styles
export const PaymentMethodsContainer = styled.View`
  gap: 32px;
  margin-bottom: 48px;
  flex: 1;
`;

export const PaymentMethodCard = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  border-radius: 24px;
  padding: 40px;
  border: 3px solid #e2e8f0;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.1;
  shadow-radius: 12px;
  elevation: 3;
  min-height: 140px;
  
  &:active {
    transform: scale(0.98);
    border-color: #3b82f6;
    background-color: #f8fafc;
  }
`;

export const PaymentMethodIcon = styled.View`
  width: 96px;
  height: 96px;
  border-radius: 48px;
  justify-content: center;
  align-items: center;
  margin-right: 32px;
`;

export const PaymentMethodInfo = styled.View`
  flex: 1;
`;

export const PaymentMethodTitle = styled.Text`
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
`;

export const PaymentMethodSubtitle = styled.Text`
  font-size: 20px;
  color: #64748b;
  font-weight: 500;
`;

// Installments Styles
export const InstallmentsScrollContainer = styled.View`
  flex: 1;
  margin-bottom: 32px;
`;

export const InstallmentsScrollView = styled.ScrollView`
  flex: 1;
`;

export const InstallmentCard = styled.TouchableOpacity`
  background-color: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  border: 3px solid #e2e8f0;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.1;
  shadow-radius: 12px;
  elevation: 3;
  min-height: 120px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  
  &:active {
    transform: scale(0.98);
    border-color: #3b82f6;
    background-color: #f8fafc;
  }
`;

export const InstallmentCardContent = styled.View`
  flex: 1;
`;

export const InstallmentMainText = styled.Text`
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
`;

export const InstallmentSubText = styled.Text`
  font-size: 18px;
  color: #64748b;
  font-weight: 500;
`;

export const InstallmentAmount = styled.Text`
  font-size: 20px;
  font-weight: 700;
  color: #059669;
  text-align: right;
`;

export const InstallmentsLoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 80px 40px;
`;

export const InstallmentsLoadingText = styled.Text`
  margin-top: 24px;
  font-size: 20px;
  color: #64748b;
  font-weight: 500;
`;

export const InstallmentsErrorContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 80px 40px;
`;

export const InstallmentsErrorText = styled.Text`
  color: #dc2626;
  font-size: 20px;
  text-align: center;
  line-height: 28px;
  font-weight: 500;
`;

// Shared Styles
export const CancelButton = styled.TouchableOpacity`
  background-color: #f1f5f9;
  border-radius: 16px;
  padding: 24px 48px;
  align-items: center;
  min-height: 80px;
  justify-content: center;
  border: 2px solid #e2e8f0;
`;

export const CancelButtonText = styled.Text`
  color: #64748b;
  font-size: 20px;
  font-weight: 600;
`;
