import { useMutation } from '@tanstack/react-query';
import { api } from '../../utils/api';

export function useVehiclesDebtSettlement() {

  return useMutation({
    mutationFn: async (data: string[]): Promise<{
      success: boolean;
      message: string;
    }> => {
      const { data: response } = await api.post('/vehicles-debts/webhook/vehicles-debts/pay', {
        debtsIdList: data,
      });
      return response;
    },
    onSuccess: () => {
    },
    onError: (error: any) => {
      console.error('Erro ao liquidar debitos:', error.message);
    },
  });
}
