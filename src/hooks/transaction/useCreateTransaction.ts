import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../utils/api';
import { ICreateTransactionRequest, ITransactionResponse } from '../../types/Transaction..types';

export function useCreateTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ICreateTransactionRequest): Promise<ITransactionResponse> => {
      if (!data.service_type) {
        throw new Error('Tipo da transação é obrigatório');
      }

      if (data.amount < 0) {
        throw new Error('Valor deve ser maior ou igual a 0');
      }

      if (!data.payment_method) {
        throw new Error('Método de pagamento é obrigatório');
      }

      const { data: response } = await api.post('/transaction', data);
      return response;
    },
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ['transactions'] });
    },
    onError: (error: any) => {
      console.error('Erro ao criar transação:', error.message);
    },
  });
}
