export interface Product {
  Product: {
    id: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
    name: string;
    description: string;
    image_url: string;
    price: string;
    ingredients: {
    name: string;
    icon: string;
   }[] | null;
    category_id: string;
    franchisor_product_catalog_id: string;
    Category: {
      id: string;
      created_at: string;
      updated_at: string;
      is_active: boolean;
      name: string;
      icon: string;
      franchisor_product_catalog_id: string;
    };
  };
  quantity: number;
}[];
