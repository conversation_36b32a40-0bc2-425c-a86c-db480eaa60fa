import styled from 'styled-components/native';
import { ScrollView, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export const Container = styled.View`
  flex: 1;
  background-color: ${props => props.theme.backgroundColor};
`;

export const Header = styled(LinearGradient).attrs(props => ({
  colors: [props.theme.primaryColor, `${props.theme.primaryColor}DD`],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 1 },
}))`
  padding: 48px;
  padding-top: 80px;
  padding-bottom: 64px;
  position: relative;
  overflow: hidden;
  min-height: 180px;
`;

export const WelcomeContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
`;

export const DecorativeCircle = styled.View`
  position: absolute;
  top: -100px;
  right: -100px;
  width: 400px;
  height: 400px;
  background-color: ${props => props.theme.accentColor}20;
  border-radius: 200px;
  z-index: 0;
`;

export const UserInfo = styled.View`
  flex: 1;
  margin-right: 24px;
  justify-content: center;
`;

export const Avatar = styled.View`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: ${props => props.theme.accentColor};
  align-items: center;
  justify-content: center;
  shadow-color: #000;
  shadow-offset: 0px 6px;
  shadow-opacity: 0.15;
  shadow-radius: 12px;
  elevation: 6;
  border: 3px solid ${props => props.theme.backgroundColor}30;
`;

export const Content = styled(ScrollView)`
  flex: 1;
  padding: 32px;
`;

export const MenuGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: space-between;
`;

export const MenuCard = styled(TouchableOpacity)`
  background-color: ${props => props.theme.backgroundColor};
  border-radius: 20px;
  padding: 28px;
  width: 48%;
  min-height: 200px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.12;
  shadow-radius: 12px;
  elevation: 6;
  border: 1px solid ${props => props.theme.secondaryColor}20;
  position: relative;
`;

interface IconContainerProps {
  backgroundColor: string;
}

export const IconContainer = styled.View<IconContainerProps>`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: ${props => props.backgroundColor};
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
`;

export const MenuContent = styled.View`
  flex: 1;
  margin-bottom: 16px;
`;

export const MenuTitle = styled.Text`
  font-size: 22px;
  font-weight: 700;
  color: ${props => props.theme.textColor};
  margin-bottom: 12px;
  line-height: 28px;
`;

export const MenuDescription = styled.Text`
  font-size: 16px;
  color: ${props => props.theme.textColor};
  line-height: 22px;
  opacity: 0.8;
`;
