import { Dimensions, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const ModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
`;

export const ModalContainer = styled.View`
  width: ${screenWidth * 0.9}px;
  max-height: ${screenHeight * 0.9}px;
  background-color: white;
  border-radius: 25px;
  overflow: hidden;
  shadow-color: #000;
  shadow-offset: 0px 8px;
  shadow-opacity: 0.25;
  shadow-radius: 15px;
  elevation: 15;
`;

export const ModalHeader = styled.View`
  flex-direction: row;
  justify-content: flex-end;
  padding: 30px 30px 0 30px;
`;

export const CloseButton = styled(TouchableOpacity)`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #f8f9fa;
  justify-content: center;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

export const ModalContent = styled.View`
  padding: 20px 40px 40px 40px;
`;

export const ProductImageContainer = styled.View`
  width: 100%;
  height: 300px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 30px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 5;
`;

export const ProductImage = styled.Image`
  width: 100%;
  height: 100%;
  resize-mode: cover;
`;

export const ProductName = styled.Text`
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 20px;
  line-height: 38px;
`;

export const PriceContainer = styled.View`
  align-items: center;
  margin-bottom: 30px;
`;

export const PriceText = styled.Text`
  font-size: 36px;
  font-weight: bold;
  color: ${props => props.theme.primaryColor};
`;

export const DescriptionContainer = styled.View`
  margin-bottom: 30px;
  padding: 25px;
  background-color: #f8f9fa;
  border-radius: 15px;
`;

export const DescriptionTitle = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15px;
`;

export const DescriptionText = styled.Text`
  font-size: 20px;
  color: #666666;
  line-height: 26px;
`;

export const QuantityContainer = styled.View`
  margin-bottom: 30px;
  padding: 25px;
  background-color: #ffffff;
  border-radius: 15px;
  border: 2px solid #f0f0f0;
  align-items: center;
`;

export const QuantityLabel = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15px;
`;

export const QuantityRow = styled.View`
  flex-direction: row;
  align-items: center;
`;

export const QuantityButton = styled(TouchableOpacity)<{ disabled?: boolean }>`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: ${props => props.disabled ? '#f5f5f5' : '#ffffff'};
  border: 2px solid ${props => props.disabled ? '#e0e0e0' : `${props.theme.primaryColor}`};
  justify-content: center;
  align-items: center;
  opacity: ${props => props.disabled ? 0.5 : 1};
`;

export const QuantityButtonText = styled.Text`
  font-size: 28px;
  font-weight: bold;
  color: #333333;
`;

export const QuantityText = styled.Text`
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  margin: 0 30px;
  min-width: 60px;
  text-align: center;
`;

export const TotalPriceText = styled.Text`
  font-size: 28px;
  font-weight: bold;
  color: ${props => props.theme.primaryColor};
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 15px;
`;

export const AddToCartButton = styled(TouchableOpacity)`
  background-color: ${props => props.theme.primaryColor};
  padding: 25px;
  border-radius: 20px;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.15;
  shadow-radius: 8px;
  elevation: 4;
`;

export const AddToCartText = styled.Text`
  color: white;
  font-size: 24px;
  font-weight: bold;
`;
