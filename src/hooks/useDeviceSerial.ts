import { useEffect, useState } from 'react';
import DeviceInfo from 'react-native-device-info';

export const useDeviceSerial = () => {
  const [serialNumber, setSerialNumber] = useState<string | null>(null);

  const getDeviceSerial = async (): Promise<string> => {
    try {
      const serial = await DeviceInfo.getAndroidId();
      console.log('Número de série do dispositivo:', serial);
      return serial;
    } catch (error) {
      console.error('Erro ao obter o número de série do dispositivo:', error);
      return 'Desconhecido';
    }
  };

  useEffect(() => {
    getDeviceSerial().then(setSerialNumber);
  }, []);

  return { serialNumber };
};
