import React, { ReactNode } from 'react';
import { Modal, type ModalProps, Animated } from 'react-native';
import { X, ArrowLeft } from 'lucide-react-native';
import {
  DrawerOver<PERSON>,
  DrawerContainer,
  Drag<PERSON>ndicator,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>Button,
  BackButton,
} from './styles';

interface BaseDrawerProps {
  visible?: boolean;
  onClose?: () => void;
  onBack?: () => void;
  title?: string;
  children: ReactNode;
  transparent?: boolean;
  animationType?: ModalProps['animationType'];
  showBackButton?: boolean;
  showCloseButton?: boolean;
}

export function BaseDrawer({
  visible = false,
  onClose,
  onBack,
  title,
  children,
  transparent = true,
  animationType = 'slide',
  showBackButton = false,
  showCloseButton = true,
}: BaseDrawerProps) {
  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}
    >
      <DrawerOverlay>
        <Animated.View
          style={{
            flex: 1,
            justifyContent: 'flex-end',
          }}
        >
          <DrawerContainer>
            <DragIndicator />

            <DrawerContent>
              <DrawerHeader>
                {showBackButton && onBack && (
                  <BackButton onPress={onBack}>
                    <ArrowLeft size={32} color="#64748b" />
                  </BackButton>
                )}

                {title && <DrawerTitle>{title}</DrawerTitle>}

                {showCloseButton && onClose && (
                  <CloseButton onPress={onClose}>
                    <X size={32} color="#64748b" />
                  </CloseButton>
                )}
              </DrawerHeader>

              {children}
            </DrawerContent>
          </DrawerContainer>
        </Animated.View>
      </DrawerOverlay>
    </Modal>
  );
}
