import React from 'react';
import { ActivityIndicator } from 'react-native';
import {
  InstallmentsScrollContainer,
  InstallmentsScrollView,
  InstallmentCard,
  InstallmentCardContent,
  InstallmentMainText,
  InstallmentSubText,
  InstallmentAmount,
  InstallmentsLoadingContainer,
  InstallmentsLoadingText,
  InstallmentsErrorContainer,
  InstallmentsErrorText,
  CancelButton,
  CancelButtonText,
} from './styles';
import { useCalculateInstallments } from '../../../hooks/useCalculateInstallments';
import { formatCurrency } from '../../../utils/formatCurrency';
import { convertToReais } from '../../../utils/convertToReais';
import type { ServicesTypeEnum } from '../../../types/enums/services-type.enum';

export interface InstallmentOption {
  installment_qty: number;
  total_amount: number;
}

interface InstallmentsContentProps {
  onInstallmentSelect: (installment: InstallmentOption) => void;
  onCancel: () => void;
  amount: number;
  service_type: ServicesTypeEnum;
  cancelLabel?: string;
  visible: boolean;
}

export function InstallmentsContent({
  onInstallmentSelect,
  onCancel,
  amount,
  service_type,
  cancelLabel = 'Cancelar',
  visible,
}: InstallmentsContentProps) {
  const { data: installments, isLoading, error } = useCalculateInstallments(
    { amount, service_type },
    visible
  );

  const formatInstallmentText = (installment: InstallmentOption) => {
    const installmentValue = installment.total_amount / installment.installment_qty;

    if (installment.installment_qty === 1) {
      return 'À vista';
    }

    if (installment.installment_qty === 0) {
      return 'Débito';
    }

    return `${installment.installment_qty}x de ${formatCurrency(convertToReais(installmentValue))}`;
  };

  const getInstallmentSubtext = (installment: InstallmentOption) => {
    if (installment.installment_qty === 1) {
      return 'Pagamento à vista';
    }

    if (installment.installment_qty === 0) {
      return 'Cartão de débito';
    }

    return `Total: ${formatCurrency(convertToReais(installment.total_amount))}`;
  };

  if (isLoading) {
    return (
      <>
        <InstallmentsLoadingContainer>
          <ActivityIndicator size="large" color="#3b82f6" />
          <InstallmentsLoadingText>Calculando parcelas...</InstallmentsLoadingText>
        </InstallmentsLoadingContainer>

        <CancelButton onPress={onCancel}>
          <CancelButtonText>{cancelLabel}</CancelButtonText>
        </CancelButton>
      </>
    );
  }

  if (error) {
    return (
      <>
        <InstallmentsErrorContainer>
          <InstallmentsErrorText>
            Erro ao carregar opções de parcelamento.{'\n'}
            Tente novamente.
          </InstallmentsErrorText>
        </InstallmentsErrorContainer>

        <CancelButton onPress={onCancel}>
          <CancelButtonText>{cancelLabel}</CancelButtonText>
        </CancelButton>
      </>
    );
  }

  return (
    <>
      {installments && (
        <InstallmentsScrollContainer>
          <InstallmentsScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {installments.map((installment) => (
              <InstallmentCard
                key={installment.installment_qty}
                onPress={() => onInstallmentSelect(installment)}
              >
                <InstallmentCardContent>
                  <InstallmentMainText>
                    {formatInstallmentText(installment)}
                  </InstallmentMainText>
                  <InstallmentSubText>
                    {getInstallmentSubtext(installment)}
                  </InstallmentSubText>
                </InstallmentCardContent>
                <InstallmentAmount>
                  {formatCurrency(convertToReais(installment.total_amount))}
                </InstallmentAmount>
              </InstallmentCard>
            ))}
          </InstallmentsScrollView>
        </InstallmentsScrollContainer>
      )}

      <CancelButton onPress={onCancel}>
        <CancelButtonText>{cancelLabel}</CancelButtonText>
      </CancelButton>
    </>
  );
}
