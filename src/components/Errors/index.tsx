import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import {AlertTriangle, RefreshCw} from 'lucide-react-native';
import styled from 'styled-components';

const RetryButton = styled.TouchableOpacity`
  background-color: #3498db;
  padding-vertical: 12px;
  padding-horizontal: 24px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  margin-top: 20px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

const RetryButtonText = styled.Text`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-left: 8px;
`;

const ErrorScreen = ({
  title = 'Algo deu errado',
  subtitle = 'Não foi possível carregar o conteúdo. Por favor, tente novamente.',
  onRetry,
  retryText = 'Tentar Novamente',
}: {
  title?: string;
  subtitle?: string;
  onRetry?: () => void;
  retryText?: string;
}) => {
  const [retryKey, setRetryKey] = useState(0);

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      setRetryKey(prev => prev + 1);
    }
  };

  return (
    <SafeAreaView style={styles.container} key={`error-screen-${retryKey}`}>
      <View style={styles.content}>
        <AlertTriangle size={80} color="#E74C3C" style={styles.icon} />
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subtitle}>{subtitle}</Text>
        <RetryButton onPress={handleRetry}>
          <RefreshCw size={18} color="#fff" />
          <RetryButtonText>{retryText}</RetryButtonText>
        </RetryButton>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 30,
    maxWidth: 350,
  },
  icon: {
    marginBottom: 20,
    opacity: 0.9,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ErrorScreen;
