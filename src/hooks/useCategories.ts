import { useQuery } from '@tanstack/react-query';
import { Category } from '../types/Category';
import { api } from '../utils/api';

export function useCategories(options = {}) {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async (): Promise<Category[]> => {
      const { data } = await api.get('/ticketeira/category');
      return data.categories;
    },
    refetchOnWindowFocus: true,
    ...options,
  });
}
