import React, { useState, useEffect, useRef } from 'react';
import { Dimensions, ScrollView } from 'react-native';
import styled from 'styled-components/native';

const { width, height } = Dimensions.get('window');

const Container = styled.View`
  flex: 1;
  background-color: #000000;
`;

const CarouselContainer = styled.View`
  flex: 1;
`;

const SlideContainer = styled.View`
  width: ${width}px;
  height: 100%;
`;

const BackgroundImage = styled.ImageBackground`
  flex: 1;
  justify-content: flex-end;
  padding: 40px 20px;
`;

const Overlay = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
`;

const ContentContainer = styled.View`
  align-items: center;
  z-index: 1;
`;

const Title = styled.Text`
  font-size: 32px;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 16px;
  line-height: 38px;
`;


const GetStartedButton = styled.TouchableOpacity`
  background-color: #ff5722;
  border-radius: 14px;
  padding: 16px 60px;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  max-width: 400px;
`;

const GetStartedButtonText = styled.Text`
  color: white;
  font-size: 24px;
  font-weight: bold;
`;

const DotsContainer = styled.View`
  flex-direction: row;
  justify-content: center;
  margin-bottom: 20px;
`;

const Dot = styled.View`
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 4px;
`;

const ActiveDot = styled(Dot)`
  background-color: #ff5722;
  width: 24px;
`;

interface GettingStartedScreenProps {
  onGetStarted: () => void;
}

const bannerData = [
  {
    id: 1,
    image: 'https://img.cdndsgni.com/preview/10000741.jpg',
    title: 'Pizzas Deliciosas',
  },
  {
    id: 2,
    image: 'https://site.getnet.com.br/wp-content/uploads/2022/11/Maquininha-de-cartao-pra-home.jpg',
    title: 'Empréstimo Flash no cartão de crédito',
  },
  {
    id: 3,
    image: 'https://scontent.fcgb1-1.fna.fbcdn.net/v/t1.6435-9/57165982_2424635704221903_282836086206496768_n.jpg?_nc_cat=102&ccb=1-7&_nc_sid=833d8c&_nc_eui2=AeHlU5dMMlXjrBdsLCQc4F5LZ7NM9CoBMuVns0z0KgEy5QkfESG5yUWUxjZ_HTdv8hTYVg8QXtDclZSQ2MyE_fNV&_nc_ohc=NZXJTmiyaBkQ7kNvwEdB1y3&_nc_oc=AdnL3sKBm5WZ5LnUL5Oe89iDyy_2fxrSxTus02TMpAiInhz1ex3tBZI73FffUlLA-Hg&_nc_zt=23&_nc_ht=scontent.fcgb1-1.fna&_nc_gid=VRM55R8xSFeWMTOOHH8S0Q&oh=00_AfSDDxBhVnZFJpjx27gQ3VVocFXEwcuvaQBxl5YF3A_p9Q&oe=68A8E9A2',
    title: 'Colecionáveis Do São Paulo FC',
  },
];

export default function GettingStartedScreen({ onGetStarted }: GettingStartedScreenProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    startAutoScroll();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const startAutoScroll = () => {
    intervalRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % bannerData.length;
        scrollViewRef.current?.scrollTo({
          x: nextIndex * width,
          animated: true,
        });
        return nextIndex;
      });
    }, 4000);
  };

  const stopAutoScroll = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const handleScroll = (event: any) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = Math.floor(event.nativeEvent.contentOffset.x / slideSize);
    setCurrentIndex(index);
  };

  const handleScrollBeginDrag = () => {
    stopAutoScroll();
  };

  const handleScrollEndDrag = () => {
    startAutoScroll();
  };

  return (
    <Container>
      <CarouselContainer>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          scrollEventThrottle={16}
        >
          {bannerData.map((banner, index) => (
            <SlideContainer key={banner.id}>
              <BackgroundImage
                source={{ uri: banner.image }}
                resizeMode="cover"
              >
                <Overlay />
                <ContentContainer>
                  <Title>{banner.title}</Title>
                  <DotsContainer>
                    {bannerData.map((_, dotIndex) => (
                      dotIndex === currentIndex ? (
                        <ActiveDot key={dotIndex} />
                      ) : (
                        <Dot key={dotIndex} />
                      )
                    ))}
                  </DotsContainer>

                  <GetStartedButton onPress={onGetStarted}>
                    <GetStartedButtonText>Começar</GetStartedButtonText>
                  </GetStartedButton>
                </ContentContainer>
              </BackgroundImage>
            </SlideContainer>
          ))}
        </ScrollView>
      </CarouselContainer>
    </Container>
  );
}
