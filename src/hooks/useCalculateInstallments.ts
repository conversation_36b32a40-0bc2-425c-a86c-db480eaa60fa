import { useQuery } from '@tanstack/react-query';
import { api } from '../utils/api';

interface InstallmentOption {
  installment_qty: number;
  total_amount: number;
}

interface CalculateInstallmentsParams {
  amount: number;
  service_type: string;
}

export function useCalculateInstallments(params: CalculateInstallmentsParams, enabled: boolean = true) {
  return useQuery({
    queryKey: ['calculate-installments', params.amount, params.service_type],
    queryFn: async (): Promise<InstallmentOption[]> => {
      const { data } = await api.post('/transaction/calculate-installments-fees', {
        amount: params.amount,
        service_type: params.service_type,
      });
      return data;
    },
    enabled,
    refetchOnWindowFocus: true,
  });
}
