import { useMutation } from '@tanstack/react-query';
import { useCreateTransaction } from './useCreateTransaction';
import { ICreateTransactionRequest, ITicketeiraProduct, ITransactionResponse } from '../../types/Transaction..types';
import { useDeviceSerial } from '../useDeviceSerial';

export function useCreateTicketeiraTransaction() {
  const createTransactionMutation = useCreateTransaction();
  const { serialNumber } = useDeviceSerial();

  return useMutation({
    mutationFn: async ({
      amount,
      installment_qty,
      products,
      payment_method,
      payment_reference,
      payment_status = 'PENDING',
      status = 'PENDING',
    }: {
      amount: number;
      installment_qty: number;
      products: ITicketeiraProduct[];
      payment_method: 'PIX' | 'CARD';
      payment_reference?: string;
      payment_status?: ICreateTransactionRequest['payment_status'];
      status?: ICreateTransactionRequest['status'];
    }): Promise<ITransactionResponse> => {
      const transactionData: ICreateTransactionRequest = {
        service_type: 'TICKETEIRA',
        deviceSerial: serialNumber!,
        installment_qty,
        products,
        amount,
        payment_method,
        payment_reference,
        payment_status,
        status,
      };

      return createTransactionMutation.mutateAsync(transactionData);
    },
  });
}
