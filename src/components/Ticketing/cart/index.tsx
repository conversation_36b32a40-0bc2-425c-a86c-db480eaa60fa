import {ShoppingCart, TrashIcon} from 'lucide-react-native';
import React, {useEffect} from 'react';
import {FlatList, Modal, TouchableWithoutFeedback, View} from 'react-native';
import {ICartItem} from '../../../types/CartItem';
import {formatCurrency} from '../../../utils/formatCurrency';
import {
  CartContainer,
  CartContent,
  CartFooter,
  CartHeader,
  CartHeaderTitle,
  CartItem as CartItemComponent,
  CartItemDetails,
  CartItemImage,
  CartItemInfo,
  CartItemName,
  CartItemPrice,
  CartItemQuantity,
  CartOverlay,
  CheckoutButton,
  CheckoutButtonText,
  CloseButton,
  CloseButtonText,
  ContinueShoppingButton,
  ContinueShoppingText,
  EmptyCart,
  EmptyCartSubtext,
  EmptyCartText,
  QuantityButton,
  QuantityButtonText,
  QuantityContainer,
  RemoveButton,
  Separator,
  TotalContainer,
  TotalLabel,
  TotalValue,
} from './styles';
import { BaseDrawer } from '../../BaseDrawler';

interface CartModalProps {
  visible: boolean;
  cartItems: ICartItem[];
  isDisabled?: boolean;
  onClose: () => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: () => void;
}

export default function CartModal({
  visible,
  isDisabled,
  onClose,
  cartItems,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout,
}: CartModalProps) {
  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + parseFloat(item.Product.price) * item.quantity;
    }, 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const renderCartItem = ({item}: {item: ICartItem}) => (
    <CartItemComponent>
      <CartItemImage source={{uri: item.Product.image_url}} />
      <CartItemDetails>
        <CartItemInfo>
          <CartItemName numberOfLines={2}>{item.Product.name}</CartItemName>
          <CartItemPrice>
            {formatCurrency(parseFloat(item.Product.price) / 100)}
          </CartItemPrice>
        </CartItemInfo>

        <QuantityContainer>
          <QuantityButton
            onPress={() =>
              onUpdateQuantity(item.Product.id, Math.max(1, item.quantity - 1))
            }>
            <QuantityButtonText>−</QuantityButtonText>
          </QuantityButton>

          <CartItemQuantity>{item.quantity}</CartItemQuantity>

          <QuantityButton
            onPress={() =>
              onUpdateQuantity(item.Product.id, item.quantity + 1)
            }>
            <QuantityButtonText>+</QuantityButtonText>
          </QuantityButton>

          <RemoveButton onPress={() => onRemoveItem(item.Product.id)}>
            <TrashIcon color={'#FF5722'} />
          </RemoveButton>
        </QuantityContainer>
      </CartItemDetails>
    </CartItemComponent>
  );

  const renderEmptyCart = () => (
    <EmptyCart>
      <ShoppingCart style={{marginBottom: 20}} size={80} />
      <EmptyCartText>Seu carrinho está vazio</EmptyCartText>
      <EmptyCartSubtext>
        Adicione alguns produtos deliciosos{'\n'}para continuar sua compra
      </EmptyCartSubtext>
    </EmptyCart>
  );

  return (
    <BaseDrawer
      visible={visible}
      animationType="slide"
      transparent={true}
      title={`Meu Carrinho (${cartItems.length > 0 ? getTotalItems() : 0})`}
      onBack={onClose}
      >
      <CartOverlay>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }} />
        </TouchableWithoutFeedback>

        <CartContainer>
          <CartContent>
            {cartItems.length === 0 ? (
              renderEmptyCart()
            ) : (
              <FlatList
                data={cartItems}
                renderItem={renderCartItem}
                keyExtractor={item => item.Product.id}
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <Separator />}
                contentContainerStyle={{paddingBottom: 20}}
              />
            )}
          </CartContent>

          {cartItems.length > 0 && (
            <CartFooter>
              <TotalContainer>
                <TotalLabel>Total do Pedido:</TotalLabel>
                <TotalValue>
                  {formatCurrency(calculateTotal() / 100)}
                </TotalValue>
              </TotalContainer>

              <CheckoutButton onPress={onCheckout} disabled={isDisabled}>
                {
                  isDisabled ? (
                    <CheckoutButtonText>Finalizando Compra...</CheckoutButtonText>
                  ) : (
                    <CheckoutButtonText>
                      Finalizar Pedido •{' '}
                      {formatCurrency(calculateTotal() / 100)}
                    </CheckoutButtonText>
                  )
                }
              </CheckoutButton>

              <ContinueShoppingButton onPress={onClose} disabled={isDisabled}>
                <ContinueShoppingText>
                  Continuar Comprando
                </ContinueShoppingText>
              </ContinueShoppingButton>
            </CartFooter>
          )}
        </CartContainer>
      </CartOverlay>
    </BaseDrawer>
  );
}
