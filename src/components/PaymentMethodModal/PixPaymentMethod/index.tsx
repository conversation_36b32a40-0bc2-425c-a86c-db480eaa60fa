import {<PERSON><PERSON><PERSON><PERSON>, CheckCircle2, Clock, Printer} from 'lucide-react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import QRCode from 'react-native-qrcode-svg';

import {PaymentMethod} from '..';
import {printPaymentReceipt} from '../../../services/sunmiPrinter/receipt/printPixReceipt';
import {convertToReais} from '../../../utils/convertToReais';
import {formatCurrency} from '../../../utils/formatCurrency';
import {Button} from '../../Button';
import {Text} from '../../Text';
import {
  AmountDisplay,
  AnimatedStatusContainer,
  BackButton,
  Container,
  Content,
  Header,
  InstructionText,
  Card,
  PaymentCard,
  QRCodeWrapper,
  SuccessContent,
  Timer,
  WaitingContent,
} from './styles';

interface PixPaymentScreenProps {
  onBack: () => void;
  onPaymentSuccess: (paymentMethod: PaymentMethod) => Promise<void> | void;
  amount: number;
  transactionId?: string;
  onPrintReceipt?: () => void;
}

const statusColors = {
  success: ['#22c55e', '#16a34a'],
  waiting: ['#d73035', '#c0392b'],
};

export function PixPaymentScreen({
  onBack,
  onPaymentSuccess,
  amount,
  transactionId,
  onPrintReceipt,
}: PixPaymentScreenProps) {
  const [paymentStatus, setPaymentStatus] = useState<'waiting' | 'success'>(
    'waiting',
  );
  const [timeLeft, setTimeLeft] = useState(300);
  const [isPrinting, setIsPrinting] = useState(false);
  const [hasAutoprinted, setHasAutoprinted] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const pixCode = `00020126360014BR.GOV.BCB.PIX0114+5511999999999520400005303986540${amount
    .toFixed(2)
    .replace(
      '.',
      '',
    )}5802BR5925NOME DO RECEBEDOR6009SAO PAULO62070503***6304ABCD`;

  const formatTimer = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  };

  const handlePrinReceipt = useCallback(async (): Promise<void> => {
    if (onPrintReceipt) {
      return onPrintReceipt();
    }

    setIsPrinting(true);
    try {
      await printPaymentReceipt(amount, transactionId, {
        paymentMethod: 'PIX',
        establishmentName: 'CLAUDIO',
        establishmentCNPJ: '12.345.678/0001-90',
        establishmentEmail: '<EMAIL>',
      });
      Alert.alert('Sucesso', 'Comprovante impresso com sucesso!');
    } catch (error) {
      console.error(error);
    } finally {
      setIsPrinting(false);
    }
  }, [amount, transactionId, onPrintReceipt]);

  useEffect(() => {
    if (paymentStatus === 'waiting' && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      Alert.alert('Tempo Esgotado', 'O tempo para pagamento expirou.', [
        {text: 'OK', onPress: onBack},
      ]);
    }
  }, [timeLeft, paymentStatus, onBack]);

  useEffect(() => {
    if (paymentStatus === 'waiting') {
      const paymentTimer = setTimeout(
        () => setPaymentStatus('success'),
        10_000,
      );
      return () => clearTimeout(paymentTimer);
    }
  }, [paymentStatus]);

  useEffect(() => {
    if (paymentStatus === 'success' && !hasAutoprinted) {
      handlePrinReceipt();
      setHasAutoprinted(true);
    }
  }, [paymentStatus, hasAutoprinted, handlePrinReceipt]);

  useEffect(() => {
    if (paymentStatus === 'success') {
      const timer = setTimeout(async () => {
        await onPaymentSuccess('PIX');
        onBack();
      }, 10_000);
      return () => clearTimeout(timer);
    }
  }, [paymentStatus, onPaymentSuccess, onBack]);

  return (
    <Container>
      <LinearGradient
        colors={['#D73035', '#B71C1C']}
        style={{paddingTop: 80, paddingHorizontal: 48, paddingBottom: 48}}>
        <Header>
          {paymentStatus === 'waiting' && (
            <BackButton onPress={onBack}>
              <ArrowLeft size={32} color="#fff" />
            </BackButton>
          )}
          <Text size={28} color="#fff" weight="700">
            {paymentStatus === 'waiting'
              ? 'Pagamento PIX'
              : 'Pagamento Aprovado'}
          </Text>
        </Header>
      </LinearGradient>

      <Content>
        <PaymentCard>
          <AnimatedStatusContainer colors={statusColors[paymentStatus]}>
            {paymentStatus === 'success' ? (
              <CheckCircle2 size={100} color="#fff" />
            ) : (
              <Clock size={80} color="#fff" />
            )}
          </AnimatedStatusContainer>

          {paymentStatus === 'waiting' ? (
            <WaitingContent>
              <InstructionText size={42} weight="700">
                Escaneie para Pagar
              </InstructionText>
              <AmountDisplay>
                <LinearGradient
                  colors={['#D73035', '#B71C1C']}
                  style={{
                    borderRadius: 20,
                    padding: 32,
                    width: '100%',
                    alignItems: 'center',
                  }}>
                  <Text size={48} weight="700" color="#fff">
                    {formatCurrency(convertToReais(amount))}
                  </Text>
                </LinearGradient>
              </AmountDisplay>
              <QRCodeWrapper>
                <QRCode
                  value={pixCode}
                  size={350}
                  backgroundColor="white"
                  color="black"
                />
              </QRCodeWrapper>
              <Timer>
                <Clock size={32} color="#D73035" style={{marginRight: 12}} />
                <Text size={32} color="#D73035" weight="600">
                  {formatTimer(timeLeft)}
                </Text>
              </Timer>
              <InstructionText size={24} color="#666">
                Abra o app do seu banco e aponte a câmera para o código QR.
              </InstructionText>
            </WaitingContent>
          ) : (
            <SuccessContent>
              <InstructionText size={42} weight="700" color="#4CAF50">
                Pagamento Realizado!
              </InstructionText>
              <InstructionText
                size={24}
                color="#666"
                weight="600"
                style={{marginTop: -16}}>
                Sua transação foi processada com sucesso.
              </InstructionText>
              <Card>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: 16,
                  }}>
                  <Text size={20} color="#666">
                    Valor pago:
                  </Text>
                  <Text size={20} weight="700" color="#4CAF50">
                    {formatCurrency(convertToReais(amount))}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: 16,
                  }}>
                  <Text size={20} color="#666">
                    Método:
                  </Text>
                  <Text size={20} weight="600">
                    PIX
                  </Text>
                </View>
                {transactionId && (
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: 16,
                    }}>
                    <Text size={20} color="#666">
                      Pedido:
                    </Text>
                    <Text size={20} weight="600">
                      {transactionId}
                    </Text>
                  </View>
                )}
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Text size={20} color="#666">
                    Data:
                  </Text>
                  <Text size={20} weight="600">
                    {new Date().toLocaleString('pt-BR')}
                  </Text>
                </View>
              </Card>
            </SuccessContent>
          )}
        </PaymentCard>

        {paymentStatus === 'success' && (
          <View
            style={{
              width: '100%',
              maxWidth: 700,
              marginTop: 48,
              display: 'flex',
              gap: 16,
            }}>
            <Button
              variant="gradient"
              gradientColors={['#4CAF50', '#45A049']}
              onPress={async () => {
                setIsSubmitted(true);
                await onPaymentSuccess('PIX');
                onBack();
              }}
              borderRadius={18}
              minHeight={80}
              disabled={isPrinting || isSubmitted}>
              <Text color="#fff" weight="700" size={28}>
                Finalizar
              </Text>
            </Button>

            <Button
              variant="outline"
              borderColor="#D1D5DB"
              borderWidth={2}
              onPress={handlePrinReceipt}
              borderRadius={18}
              minHeight={80}>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Printer size={28} color="#6B7281" style={{marginRight: 12}} />
                <Text color="#6B7281" weight="700" size={28}>
                  {isPrinting ? 'Imprimindo...' : 'Imprimir Comprovante'}
                </Text>
              </View>
            </Button>
          </View>
        )}
      </Content>
    </Container>
  );
}
