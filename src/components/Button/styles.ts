import styled from 'styled-components/native';
import LinearGradient from 'react-native-linear-gradient';

interface ButtonProps {
  disabled?: boolean
  width?: number
  height?: number
  minHeight?: number
  borderRadius?: number
  elevation?: number
  loading?: boolean
  variant?: 'solid' | 'outline'
  borderColor?: string
  backgroundColor?: string
  borderWidth?: number
}

export const Container = styled.TouchableOpacity<ButtonProps>`
  width: ${({ width }) => width || 'auto'};
  border-radius: ${({ borderRadius }) => borderRadius || 48}px;
  height: ${({ height }) => height || 'auto'};
  min-height: ${({ minHeight }) => minHeight || 'auto'}px;
  padding: 14px 24px;
  align-items: center;
  justify-content: center;

  background: ${({ disabled, variant, theme, backgroundColor }) => {
    if (variant === 'outline') {
      return 'transparent';
    }

    if (backgroundColor) {
      return disabled ? `${theme.backgroundColor}` : backgroundColor;
    }

    return disabled ? `${theme.backgroundColor}` : theme.primaryColor;
  }};

  border-width: ${({ variant, borderWidth }) => {
    if (variant === 'outline') {
      return borderWidth || 2;
    }
    return 0;
  }}px;

  border-color: ${({ disabled, variant, borderColor, theme }) => {
    if (variant === 'outline') {
      return disabled ? `${theme.textColor}` : borderColor || theme.accentColor;
    }
    return 'transparent';
  }};
`;

export const GradientTouchable = styled.TouchableOpacity<ButtonProps>`
  width: ${({ width }) => width || 'auto'};
  border-radius: ${({ borderRadius }) => borderRadius || 48}px;
  height: ${({ height }) => height || 'auto'};
  min-height: ${({ minHeight }) => minHeight || 'auto'}px;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  overflow: hidden;
  elevation: ${({ elevation }) => elevation || 0};
`;

export const GradientBackground = styled(LinearGradient).attrs({
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0 },
})`
  padding: 14px 24px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex: 1;
`;
