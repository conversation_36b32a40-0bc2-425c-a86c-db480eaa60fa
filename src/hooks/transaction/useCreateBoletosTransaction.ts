import { useMutation } from '@tanstack/react-query';
import { useCreateTransaction } from './useCreateTransaction';
import { ICreateTransactionRequest, ITransactionResponse } from '../../types/Transaction..types';
import { useDeviceSerial } from '../useDeviceSerial';

export function useCreateBoletosTransaction() {
  const createTransactionMutation = useCreateTransaction();
  const { serialNumber } = useDeviceSerial();

  return useMutation({
    mutationFn: async ({
      amount,
      installment_qty,
      payment_method,
      payment_reference,
      payment_status = 'PENDING',
      status = 'PENDING',
    }: {
      amount: number;
      installment_qty: number;
      payment_method: 'PIX' | 'CARD';
      payment_reference?: string;
      payment_status?: ICreateTransactionRequest['payment_status'];
      status?: ICreateTransactionRequest['status'];
    }): Promise<ITransactionResponse> => {
      const transactionData: ICreateTransactionRequest = {
        service_type: 'BOLETO',
        installment_qty,
        amount,
        payment_method,
        payment_reference,
        payment_status,
        status,
        deviceSerial: serialNumber!,
      };

      return createTransactionMutation.mutateAsync(transactionData);
    },
  });
}
