import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Modal,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {useQueryClient} from '@tanstack/react-query';

export function CustomQueryDevtools() {
  const [isVisible, setIsVisible] = useState(false);
  const queryClient = useQueryClient();

  const queries = queryClient.getQueryCache().getAll();

  const invalidateQuery = (queryKey: any[]) => {
    queryClient.invalidateQueries({queryKey});
    console.log('Invalidated query:', queryKey);
  };

  const refetchQuery = (queryKey: any[]) => {
    queryClient.refetchQueries({queryKey});
    console.log('Refetched query:', queryKey);
  };

  const removeQuery = (queryKey: any[]) => {
    queryClient.removeQueries({queryKey});
    console.log('Removed query:', queryKey);
  };

  console.log('Total queries found:', queries.length);
  queries.forEach((query, index) => {
    console.log(`Query ${index}:`, {
      key: query.queryKey,
      status: query.state.status,
      hasData: !!query.state.data,
    });
  });

  if (!__DEV__) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        style={styles.floatingButton}
        onPress={() => setIsVisible(true)}>
        <Text style={styles.buttonText}>🌸</Text>
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsVisible(false)}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>
              React Query Devtools ({queries.length} queries)
            </Text>
            <TouchableOpacity onPress={() => setIsVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {queries.length === 0 ? (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Nenhuma query encontrada.</Text>
                <Text style={styles.emptySubtext}>
                  Execute algumas ações para ver as queries aparecerem aqui.
                </Text>
              </View>
            ) : (
              queries.map((query, index) => {
                const queryKey = Array.isArray(query.queryKey)
                  ? query.queryKey
                  : [query.queryKey];

                const statusColor =
                  {
                    success: '#4CAF50',
                    loading: '#FF9800',
                    error: '#F44336',
                    idle: '#9E9E9E',
                  }[query.state.status] || '#9E9E9E';

                return (
                  <View
                    key={`${JSON.stringify(queryKey)}-${index}`}
                    style={styles.queryItem}>
                    <View style={styles.queryHeader}>
                      <View
                        style={[
                          styles.statusDot,
                          {backgroundColor: statusColor},
                        ]}
                      />
                      <Text style={styles.queryKey}>
                        {JSON.stringify(queryKey)}
                      </Text>
                    </View>

                    <Text style={[styles.queryStatus, {color: statusColor}]}>
                      Status: {query.state.status}
                    </Text>

                    <Text style={styles.queryData}>
                      Data: {query.state.data ? '✅ Carregado' : '❌ Sem dados'}
                    </Text>

                    <Text style={styles.queryInfo}>
                      Último update:{' '}
                      {query.state.dataUpdatedAt
                        ? new Date(
                            query.state.dataUpdatedAt,
                          ).toLocaleTimeString()
                        : 'Nunca'}
                    </Text>

                    <View style={styles.buttonRow}>
                      <TouchableOpacity
                        style={[styles.actionButton, styles.invalidateButton]}
                        onPress={() => invalidateQuery(queryKey)}>
                        <Text style={styles.actionButtonText}>Invalidate</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.actionButton, styles.refetchButton]}
                        onPress={() => refetchQuery(queryKey)}>
                        <Text style={styles.actionButtonText}>Refetch</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.actionButton, styles.removeButton]}
                        onPress={() => removeQuery(queryKey)}>
                        <Text style={styles.actionButtonText}>Remove</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })
            )}
          </ScrollView>

          <View style={styles.globalActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.globalButton]}
              onPress={() => {
                queryClient.invalidateQueries();
                console.log('Invalidated all queries');
              }}>
              <Text style={styles.actionButtonText}>Invalidate All</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.globalButton]}
              onPress={() => {
                queryClient.clear();
                console.log('Cleared all cache');
              }}>
              <Text style={styles.actionButtonText}>Clear Cache</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  floatingButton: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 4,
    zIndex: 1000,
  },
  buttonText: {
    fontSize: 24,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingTop: 50,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  closeButton: {
    fontSize: 24,
    color: '#666',
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  queryItem: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  queryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  queryKey: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  queryStatus: {
    fontSize: 12,
    marginBottom: 4,
    fontWeight: '600',
  },
  queryData: {
    fontSize: 12,
    marginBottom: 4,
    color: '#666',
  },
  queryInfo: {
    fontSize: 10,
    marginBottom: 12,
    color: '#999',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  invalidateButton: {
    backgroundColor: '#FFA726',
  },
  refetchButton: {
    backgroundColor: '#42A5F5',
  },
  removeButton: {
    backgroundColor: '#EF5350',
  },
  globalButton: {
    backgroundColor: '#9C27B0',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  globalActions: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    gap: 10,
  },
});
