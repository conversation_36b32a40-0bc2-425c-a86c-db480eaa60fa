import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

export const api = axios.create({
  baseURL: 'https://hml-totemapi.pagway.com.br',
});

let globalLogout: (() => void) | null = null;

export const setGlobalLogout = (logoutFn: () => void) => {
  globalLogout = logoutFn;
};

api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    console.log('[API error]', error);
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('accessToken');
      delete api.defaults.headers.common.Authorization;

      if (globalLogout) {
        globalLogout();
      }
    }

    return Promise.reject(error);
  }
);
