import React, { createContext, useContext, ReactNode } from 'react';

interface AuthContextProps {
  logout: () => void;
}

const AuthContext = createContext<AuthContextProps | null>(null);

interface AuthProviderProps {
  children: ReactNode;
  onLogout: () => void;
}

export default function AuthProvider({ children, onLogout }: AuthProviderProps) {
  return (
    <AuthContext.Provider value={{ logout: onLogout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext deve ser usado dentro de AuthProvider');
  }
  return context;
}
