import type React from 'react';
import { useEffect, useRef } from 'react';
import { Animated, Dimensions } from 'react-native';
import styled from 'styled-components/native';

const { width: screenWidth } = Dimensions.get('window');

export const LoadingContainer = styled.View`
  flex: 1;
  background-color: #ffffff;
`;

export const LoadingHeader = styled.View`
  padding: 40px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: #e0e0e0;
`;

export const LoadingBackButton = styled(Animated.View)`
  margin-right: 32px;
  width: 96px;
  height: 96px;
  border-radius: 48px;
  background-color: #f5f5f5;
`;

export const LoadingWelcomeContainer = styled.View`
  flex: 1;
`;

export const LoadingWelcomeText = styled(Animated.View)`
  height: 42px;
  width: 250px;
  border-radius: 6px;
  background-color: #f5f5f5;
  margin-bottom: 8px;
`;

export const LoadingSubtitleText = styled(Animated.View)`
  height: 24px;
  width: 350px;
  border-radius: 4px;
  background-color: #f5f5f5;
  margin-top: 8px;
`;

export const LoadingCartButton = styled(Animated.View)`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #f5f5f5;
`;

export const LoadingCategoryContainer = styled.View`
  padding: 30px 0;
`;

export const LoadingCategoryListContainer = styled.View`
  padding-left: 30px;
  flex-direction: row;
`;

export const LoadingCategoryItem = styled.View`
  align-items: center;
  width: 140px;
  margin-right: 20px;
`;

export const LoadingCategoryIcon = styled(Animated.View)`
  width: 100px;
  height: 100px;
  border-radius: 50px;
  background-color: #f5f5f5;
  margin-bottom: 15px;
  border: 3px solid #e9ecef;
`;

export const LoadingCategoryText = styled(Animated.View)`
  height: 18px;
  width: 100px;
  border-radius: 4px;
  background-color: #f5f5f5;
`;

export const LoadingBannerContainer = styled.View`
  height: 200px;
  position: relative;
  margin-bottom: 36px;
  padding: 0 0;
`;

export const LoadingBanner = styled(Animated.View)`
  width: ${screenWidth}px;
  height: 200px;
  border-radius: 0px;
  background-color: #f5f5f5;
`;

export const LoadingBannerControls = styled.View`
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  flex-direction: row;
  justify-content: center;
  align-items: center;
`;

export const LoadingBannerDot = styled(Animated.View)`
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: #e9ecef;
  margin: 0 4px;
`;

export const LoadingSectionTitle = styled(Animated.View)`
  height: 32px;
  width: 150px;
  border-radius: 6px;
  background-color: #f5f5f5;
  margin: 0 40px 30px 40px;
`;

export const LoadingFoodGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 20px;
`;

export const LoadingFoodItem = styled.View`
  background-color: #ffffff;
  border-radius: 20px;
  padding: 20px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.15;
  shadow-radius: 8px;
  elevation: 8;
  width: 48%;
  min-height: 320px;
  margin: 1%;
  margin-bottom: 20px;
`;

export const LoadingFoodImage = styled(Animated.View)`
  width: 100%;
  height: 180px;
  background-color: #f5f5f5;
  border-radius: 15px;
  margin-bottom: 15px;
`;

export const LoadingFoodItemContent = styled.View`
  flex: 1;
  justify-content: space-between;
`;

export const LoadingFoodName = styled(Animated.View)`
  height: 20px;
  width: 85%;
  border-radius: 4px;
  background-color: #f5f5f5;
  align-self: center;
  margin-bottom: 8px;
`;

export const LoadingFoodPrice = styled(Animated.View)`
  height: 22px;
  width: 70%;
  border-radius: 4px;
  background-color: #f5f5f5;
  align-self: center;
  margin-bottom: 15px;
`;

export const LoadingAddToCartButton = styled(Animated.View)`
  height: 48px;
  width: 100%;
  border-radius: 12px;
  background-color: #f5f5f5;
  margin-top: auto;
`;

interface LoadingTicketeiraProps {
  showHeader?: boolean;
  showCategories?: boolean;
  showBanner?: boolean;
  showProducts?: boolean;
  categoriesCount?: number;
  productsCount?: number;
}

export default function LoadingTicketeira({
  showHeader = true,
  showCategories = true,
  showBanner = true,
  showProducts = true,
  categoriesCount = 5,
  productsCount = 6,
}: LoadingTicketeiraProps) {
  const pulseAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: false,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0,
          duration: 1200,
          useNativeDriver: false,
        }),
      ]).start(() => pulse());
    };

    pulse();
  }, [pulseAnim]);

  const animatedStyle = {
    opacity: pulseAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.8],
    }),
  };

  const renderHeader = () => {
    if (!showHeader) {return null;}

    return (
      <LoadingHeader>
        <LoadingBackButton style={animatedStyle} />
        <LoadingWelcomeContainer>
          <LoadingWelcomeText style={animatedStyle} />
          <LoadingSubtitleText style={animatedStyle} />
        </LoadingWelcomeContainer>
        <LoadingCartButton style={animatedStyle} />
      </LoadingHeader>
    );
  };

  const renderCategories = () => {
    if (!showCategories) {return null;}

    return (
      <LoadingCategoryContainer>
        <LoadingCategoryListContainer>
          {Array.from({ length: categoriesCount }).map((_, index) => (
            <LoadingCategoryItem key={index}>
              <LoadingCategoryIcon style={animatedStyle} />
              <LoadingCategoryText style={animatedStyle} />
            </LoadingCategoryItem>
          ))}
        </LoadingCategoryListContainer>
      </LoadingCategoryContainer>
    );
  };

  const renderBanner = () => {
    if (!showBanner) {return null;}

    return (
      <LoadingBannerContainer>
        <LoadingBanner style={animatedStyle} />
        <LoadingBannerControls>
          {Array.from({ length: 3 }).map((_, index) => (
            <LoadingBannerDot key={index} style={animatedStyle} />
          ))}
        </LoadingBannerControls>
      </LoadingBannerContainer>
    );
  };

  const renderProducts = () => {
    if (!showProducts) {return null;}

    return (
      <>
        <LoadingSectionTitle style={animatedStyle} />
        <LoadingFoodGrid>
          {Array.from({ length: productsCount }).map((_, index) => (
            <LoadingFoodItem key={index}>
              <LoadingFoodImage style={animatedStyle} />
              <LoadingFoodItemContent>
                <LoadingFoodName style={animatedStyle} />
                <LoadingFoodPrice style={animatedStyle} />
                <LoadingAddToCartButton style={animatedStyle} />
              </LoadingFoodItemContent>
            </LoadingFoodItem>
          ))}
        </LoadingFoodGrid>
      </>
    );
  };

  return (
    <LoadingContainer>
      {renderHeader()}
      {renderCategories()}
      {renderBanner()}
      {renderProducts()}
    </LoadingContainer>
  );
}
