import { useQuery } from '@tanstack/react-query';
import { Product } from '../types/Product';
import { api } from '../utils/api';

export function useProducts(categoryId?: string, options = {}) {
  const route = !categoryId ? '/stock/list-products-by-totem' : `/stock/list-products-by-totem?categoryId=${categoryId}`;

  return useQuery({
    queryKey: ['products', categoryId || 'all'],
    queryFn: async (): Promise<Product[]> => {
      const { data } = await api.get(route);
      return data;
    },
    refetchOnWindowFocus: true,
    ...options,
  });
}
