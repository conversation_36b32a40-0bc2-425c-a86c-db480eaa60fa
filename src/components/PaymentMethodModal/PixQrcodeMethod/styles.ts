import { Dimensions } from 'react-native';
import styled from 'styled-components/native';

export const { width, height } = Dimensions.get('window');

export const Container = styled.View`
  flex: 1;
  background-color: #f8fafc;
  padding: 40px;
  justify-content: center;
  align-items: center;
`;

export const Header = styled.View`
  align-items: center;
  margin-bottom: 60px;
`;

export const Title = styled.Text`
  font-size: 36px;
  font-weight: bold;
  color: #1e293b;
  text-align: center;
  margin-bottom: 16px;
`;

export const Subtitle = styled.Text`
  font-size: 20px;
  color: #64748b;
  text-align: center;
`;

export const PaymentCard = styled.View`
  background-color: #ffffff;
  border-radius: 32px;
  padding: 40px;
  elevation: 6;
  align-items: center;
  min-width: ${width * 0.7}px;
`;

export const QRContainer = styled.View`
  background-color: #ffffff;
  padding: 30px;
  border-radius: 24px;
  border: 4px solid #e2e8f0;
  margin-bottom: 40px;
  align-items: center;
  justify-content: center;
`;

export const AmountContainer = styled.View`
  align-items: center;
  margin-bottom: 40px;
`;

export const AmountLabel = styled.Text`
  font-size: 20px;
  color: #64748b;
  margin-bottom: 8px;
`;

export const AmountValue = styled.Text`
  font-size: 42px;
  font-weight: bold;
  color: ${props => props.theme.primaryColor};
`;

export const TimerContainer = styled.View<{ urgent: boolean }>`
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => (props.urgent ? '#fef2f2' :
  `${props.theme.primaryColor}40`)};
  padding: 20px 32px;
  border-radius: 16px;
  margin-bottom: 40px;
  border: 2px solid ${(props) => (props.urgent ? '#fecaca' : props.theme.primaryColor)};
`;

export const TimerText = styled.Text<{ urgent: boolean }>`
  font-size: 24px;
  font-weight: bold;
  color: ${(props) => (props.urgent ? '#dc2626' : props.theme.primaryColor)};
  margin-left: 12px;
`;

export const InstructionsContainer = styled.View`
  align-items: center;
  max-width: 600px;
`;

export const InstructionItem = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
`;

export const InstructionNumber = styled.View`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${props => props.theme.primaryColor};
  align-items: center;
  justify-content: center;
  margin-right: 16px;
`;

export const InstructionNumberText = styled.Text`
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
`;

export const InstructionText = styled.Text`
  font-size: 18px;
  color: #475569;
  flex: 1;
  line-height: 28px;
`;

export const StatusContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${props => props.theme.primaryColor}40;
  padding: 16px 24px;
  border-radius: 12px;
  border: 2px solid ${props => props.theme.primaryColor};
  margin-top: 20px;
`;

export const StatusText = styled.Text`
  font-size: 18px;
  color: ${props => props.theme.primaryColor};
  font-weight: 600;
  margin-left: 8px;
`;

export const FooterContainer = styled.View`
  position: absolute;
  bottom: 40px;
  left: 40px;
  right: 40px;
  align-items: center;
`;

export const HelpContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: #f1f5f9;
  padding: 16px 24px;
  border-radius: 12px;
`;

export const HelpText = styled.Text`
  font-size: 18px;
  color: #64748b;
  margin-left: 8px;
  text-align: center;
`;

export const ExpiredContainer = styled.View`
  align-items: center;
  padding: 40px;
`;

export const ExpiredTitle = styled.Text`
  font-size: 48px;
  font-weight: bold;
  color: #dc2626;
  margin-top: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

export const ExpiredText = styled.Text`
  font-size: 22px;
  color: #dc2626;
  text-align: center;
  line-height: 32px;
`;

export const CancelContainer = styled.View`
  margin-top: 30px;
  width: 100%;
  align-items: center;
`;


export const CancelButtonText = styled.Text`
  color: #ef4444;
  font-size: 24px;
  font-weight: 600;
  margin-left: 8px;
`;


export const SuccessContainer = styled.View`
  align-items: center;
  padding: 40px;
`;

export const SuccessTitle = styled.Text`
  font-size: 48px;
  font-weight: bold;
  color: ${(props) => props.theme.primaryColor};
  margin-top: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

export const SuccessText = styled.Text`
  font-size: 22px;
  color: #475569;
  text-align: center;
  line-height: 32px;
  margin-bottom: 40px;
`;

export const SuccessDetails = styled.View`
  width: 100%;
  min-width: 600px;
  background-color: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
`;

export const SuccessDetailItem = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export const SuccessDetailLabel = styled.Text`
  font-size: 18px;
  color: #64748b;
  font-weight: 500;
`;

export const SuccessDetailValue = styled.Text`
  font-size: 18px;
  color: #1e293b;
  font-weight: bold;
`;
