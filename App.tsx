/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';
import { SafeAreaView, StatusBar } from 'react-native';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomQueryDevtools } from './src/components/CustomQueryDevTools';
import { Main } from './src/main';


const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

function App(): React.JSX.Element {
  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaView style={{flex: 1}}>
        <StatusBar barStyle="dark-content" />
        <Main />
      </SafeAreaView>
      <CustomQueryDevtools />
    </QueryClientProvider>
  );
}

export default App;
