import styled from 'styled-components/native';

export const Container = styled.View`
  flex: 1;
  background-color: #ffffff;
  padding: 20px;
  justify-content: center;
`;

export const LogoContainer = styled.View`
  align-items: center;
  margin-bottom: 40px;
`;

export const Logo = styled.View`
  width: 80px;
  height: 80px;
  background-color: #ff5722;
  border-radius: 40px;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
`;

export const LogoText = styled.Text`
  font-size: 24px;
  color: white;
  font-weight: bold;
`;

export const WelcomeTitle = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 8px;
`;

export const Subtitle = styled.Text`
  font-size: 16px;
  color: #666666;
  text-align: center;
  margin-bottom: 40px;
`;

export const FormContainer = styled.View`
  width: 100%;
`;

export const InputContainer = styled.View`
  margin-bottom: 20px;
`;

export const InputLabel = styled.Text`
  font-size: 16px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: 500;
`;

export const InputWrapper = styled.View`
  position: relative;
`;

export const TextInput = styled.TextInput`
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  color: #333333;
  border: 1px solid #e0e0e0;
`;

export const PasswordInput = styled(TextInput)`
  padding-right: 50px;
`;

export const EyeButton = styled.TouchableOpacity`
  position: absolute;
  right: 16px;
  top: 16px;
`;

export const RememberContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
`;

export const RememberRow = styled.View`
  flex-direction: row;
  align-items: center;
`;

export const ForgotPasswordText = styled.Text`
  font-size: 14px;
  color: #ff5722;
  font-weight: 500;
`;

export const LoginButton = styled.TouchableOpacity`
  background-color: #ff5722;
  border-radius: 12px;
  padding: 16px;
  align-items: center;
  margin-bottom: 30px;
`;

export const LoginButtonText = styled.Text`
  color: white;
  font-size: 18px;
  font-weight: bold;
`;
