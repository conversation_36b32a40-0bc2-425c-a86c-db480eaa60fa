import { Dimensions, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';

const { width, height } = Dimensions.get('window');

export const Overlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
  padding: 48px;
`;

export const ModalContainer = styled.View`
  background-color: #fff;
  border-radius: 16px;
  padding: 48px;
  width: 100%;
  max-width: 800px;
`;

export const Header = styled.View`
  align-items: center;
  margin-bottom: 48px;
`;

export const PaymentOptions = styled.View`
  gap: 24px;
  margin-bottom: 48px;
`;

export const PaymentOption = styled(TouchableOpacity)<{ selected: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 32px;
  border-radius: 16px;
  border: 3px solid ${(props) => (props.selected ? '#D73035' : '#ddd')};
  background-color: ${(props) => (props.selected ? '#FFF5F5' : '#fff')};
  min-height: 100px;
`;

export const PaymentIcon = styled.View`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #f0f0f0;
  justify-content: center;
  align-items: center;
  margin-right: 24px;
`;

export const PaymentDetails = styled.View`
  flex: 1;
`;

export const RadioButton = styled.View<{ selected: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 3px solid ${(props) => (props.selected ? '#D73035' : '#ddd')};
  background-color: ${(props) => (props.selected ? '#D73035' : 'transparent')};
  margin-left: auto;
`;

export const ButtonContainer = styled.View`
  gap: 24px;
`;

export const ModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: flex-end;
`;

export const ModalContent = styled.View`
  background-color: #ffffff;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  padding: 24px;
  max-height: ${height * 0.8}px;
`;

export const ModalHeader = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
`;

export const ModalTitle = styled.Text`
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
`;

export const PaymentModalContent = styled.View`
  background-color: #ffffff;
  border-radius: 20px;
  padding: 32px;
  margin: 20px;
  align-items: center;
`;

export const PaymentModalTitle = styled.Text`
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  text-align: center;
`;

export const PaymentMethodButton = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  width: 100%;
  border: 2px solid transparent;
`;

export const PaymentMethodText = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-left: 16px;
`;

export const CancelButton = styled.TouchableOpacity`
  background-color: #f1f5f9;
  border-radius: 16px;
  padding: 24px 48px;
  margin-top: 32px;
  align-items: center;
  min-height: 80px;
  justify-content: center;
`;

export const CancelButtonText = styled.Text`
  color: #64748b;
  font-size: 20px;
  font-weight: 600;
`;

export const DrawerOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: flex-end;
`;

export const DrawerContainer = styled.View`
  background-color: #ffffff;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  min-height: ${height * 0.75}px;
  max-height: ${height * 0.9}px;
  width: 100%;
  shadow-color: #000;
  shadow-offset: 0px -4px;
  shadow-opacity: 0.25;
  shadow-radius: 16px;
  elevation: 20;
`;

export const DragIndicator = styled.View`
  width: 80px;
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  align-self: center;
  margin-top: 16px;
  margin-bottom: 8px;
`;

export const DrawerContent = styled.View`
  flex: 1;
  padding: 32px 48px 48px 48px;
`;

export const DrawerHeader = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48px;
  padding-bottom: 24px;
  border-bottom-width: 2px;
  border-bottom-color: #f1f5f9;
`;

export const DrawerTitle = styled.Text`
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  flex: 1;
`;

export const CloseButton = styled.TouchableOpacity`
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: #f8fafc;
  justify-content: center;
  align-items: center;
  border: 2px solid #e2e8f0;
`;

export const PaymentMethodsContainer = styled.View`
  gap: 32px;
  margin-bottom: 48px;
  flex: 1;
`;

export const PaymentMethodCard = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  border-radius: 24px;
  padding: 40px;
  border: 3px solid #e2e8f0;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.1;
  shadow-radius: 12px;
  elevation: 3;
  min-height: 140px;
  
  &:active {
    transform: scale(0.98);
    border-color: #3b82f6;
    background-color: #f8fafc;
  }
`;

export const PaymentMethodIcon = styled.View`
  width: 96px;
  height: 96px;
  border-radius: 48px;
  justify-content: center;
  align-items: center;
  margin-right: 32px;
`;

export const PaymentMethodInfo = styled.View`
  flex: 1;
`;

export const PaymentMethodTitle = styled.Text`
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
`;

export const PaymentMethodSubtitle = styled.Text`
  font-size: 20px;
  color: #64748b;
  font-weight: 500;
`;
