import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>iew, TouchableWithoutFeedback, View} from 'react-native';
import {Minus, Plus, X} from 'lucide-react-native';
import {
  AddToCartButton,
  AddToCartText,
  CloseButton,
  DescriptionContainer,
  DescriptionText,
  DescriptionTitle,
  ModalContainer,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  PriceContainer,
  PriceText,
  ProductImage,
  ProductImageContainer,
  ProductName,
  QuantityButton,
  QuantityContainer,
  QuantityLabel,
  QuantityRow,
  QuantityText,
  TotalPriceText,
} from './styles';
import {Product} from '../../../types/Product';
import {formatCurrency} from '../../../utils/formatCurrency';

interface ProductDetailsModalProps {
  visible: boolean;
  product: Product | null;
  onClose: () => void;
  onAddToCart: (product: Product, quantity: number) => void;
}

export default function ProductDetailsModal({
  visible,
  product,
  onClose,
  onAddToCart,
}: ProductDetailsModalProps) {
  const [quantity, setQuantity] = useState(1);

  if (!product) {
    return null;
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    onAddToCart(product, quantity);
    setQuantity(1);
    onClose();
  };

  const unitPrice = parseFloat(product.Product.price) / 100;
  const totalPrice = unitPrice * quantity;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <ModalOverlay>
        <TouchableWithoutFeedback onPress={onClose}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
          />
        </TouchableWithoutFeedback>
        <ModalContainer>
          <ModalHeader>
            <CloseButton onPress={onClose}>
              <X size={32} color="#666666" strokeWidth={2.5} />
            </CloseButton>
          </ModalHeader>

          <ScrollView showsVerticalScrollIndicator={false}>
            <ModalContent>
              <ProductImageContainer>
                <ProductImage source={{uri: product.Product.image_url}} />
              </ProductImageContainer>

              <ProductName>{product.Product.name}</ProductName>

              <PriceContainer>
                <PriceText>{formatCurrency(unitPrice)}</PriceText>
              </PriceContainer>

              {product.Product.description && (
                <DescriptionContainer>
                  <DescriptionTitle>Descrição</DescriptionTitle>
                  <DescriptionText>
                    {product.Product.description}
                  </DescriptionText>
                </DescriptionContainer>
              )}

              <QuantityContainer>
                <QuantityLabel>Quantidade:</QuantityLabel>
                <QuantityRow>
                  <QuantityButton
                    onPress={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}>
                    <Minus size={24} color="#333333" strokeWidth={2.5} />
                  </QuantityButton>

                  <QuantityText>{quantity}</QuantityText>

                  <QuantityButton
                    onPress={() => handleQuantityChange(quantity + 1)}>
                    <Plus size={24} color="#333333" strokeWidth={2.5} />
                  </QuantityButton>
                </QuantityRow>
              </QuantityContainer>

              <TotalPriceText>
                Total: {formatCurrency(totalPrice)}
              </TotalPriceText>

              <AddToCartButton onPress={handleAddToCart}>
                <AddToCartText>
                  Adicionar ao Carrinho • {formatCurrency(totalPrice)}
                </AddToCartText>
              </AddToCartButton>
            </ModalContent>
          </ScrollView>
        </ModalContainer>
      </ModalOverlay>
    </Modal>
  );
}
