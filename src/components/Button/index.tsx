import type React from 'react';
import { ActivityIndicator } from 'react-native';
import { Text } from '../Text';
import { Container, GradientTouchable, GradientBackground } from './styles';
import { useAppTheme } from '../../contexts/ThemeContext';

interface ButtonProps {
  children: string | React.ReactNode
  onPress: () => void
  disabled?: boolean
  loading?: boolean
  width?: number
  height?: number
  minHeight?: number
  borderRadius?: number
  elevation?: number
  variant?: 'solid' | 'gradient' | 'outline'
  gradientColors?: string[]
  gradientStart?: { x: number; y: number }
  gradientEnd?: { x: number; y: number }
  borderColor?: string
  backgroundColor?: string
  borderWidth?: number
  textColor?: string
}

export function Button({
  children,
  onPress,
  disabled,
  loading,
  width,
  height,
  minHeight,
  borderRadius,
  elevation,
  variant = 'solid',
  gradientColors,
  gradientStart = { x: 0, y: 0 },
  gradientEnd = { x: 1, y: 0 },
  borderColor,
  backgroundColor,
  borderWidth,
  textColor,
}: ButtonProps) {
  const { theme } = useAppTheme();

  const defaultGradientColors = gradientColors || [theme.primaryColor, theme.secondaryColor];
  const defaultBorderColor = borderColor || theme.accentColor;
  const defaultTextColor = textColor || theme.textColor;

  if (variant === 'outline') {
    const finalTextColor = disabled ? `${theme.textColor}60` : defaultTextColor;
    return (
      <Container
        onPress={onPress}
        disabled={disabled || loading}
        width={width}
        borderRadius={borderRadius}
        height={height}
        minHeight={minHeight}
        variant="outline"
        borderColor={defaultBorderColor}
        borderWidth={borderWidth}
      >
        {!loading && (
          <Text weight="600" color={finalTextColor}>
            {children}
          </Text>
        )}
        {loading && <ActivityIndicator color={finalTextColor} />}
      </Container>
    );
  }

  const buttonContent = (
    <>
      {!loading && (
        <Text weight="600" color={theme.textColor}>
          {children}
        </Text>
      )}
      {loading && <ActivityIndicator color={theme.textColor} />}
    </>
  );

  if (variant === 'gradient') {
    return (
      <GradientTouchable
        onPress={onPress}
        disabled={disabled || loading}
        width={width}
        borderRadius={borderRadius}
        height={height}
        minHeight={minHeight}
        elevation={elevation}
      >
        <GradientBackground colors={defaultGradientColors} start={gradientStart} end={gradientEnd}>
          {buttonContent}
        </GradientBackground>
      </GradientTouchable>
    );
  }

  return (
    <Container
      onPress={onPress}
      disabled={disabled || loading}
      width={width}
      borderRadius={borderRadius}
      height={height}
      minHeight={minHeight}
      backgroundColor={backgroundColor}
      variant="solid"
    >
      {buttonContent}
    </Container>
  );
}
