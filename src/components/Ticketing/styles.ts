import { Dimensions, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';

const { width: screenWidth } = Dimensions.get('window');

export const Container = styled.View`
  flex: 1;
  background-color: #ffffff;
`;

export const Header = styled.View`
  padding: 40px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: #e0e0e0;
`;

export const BackButton = styled.TouchableOpacity`
  margin-right: 32px;
  padding: 20px;
  min-width: 96px;
  min-height: 96px;
  justify-content: center;
  align-items: center;
  border-radius: 48px;
  background-color: rgba(255, 255, 255, 0.1);
`;

export const WelcomeContainer = styled.View`
  flex: 1;
`;

export const WelcomeText = styled.Text`
  font-size: 42px;
  font-weight: bold;
  color: #333333;
`;

export const SubtitleText = styled.Text`
  font-size: 24px;
  color: #666666;
  margin-top: 8px;
`;

export const CartButton = styled(TouchableOpacity)`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  justify-content: center;
  align-items: center;
  position: relative;
`;

export const CartCount = styled.Text`
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #FF5722;
  color: white;
  font-size: 16px;
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  text-align: center;
  line-height: 30px;
`;

export const CategoryContainer = styled.View`
  padding: 30px 0;
`;

export const CategoryListContainer = styled.View`
  padding-left: 30px;
`;

export const CategoryItem = styled(TouchableOpacity)`
  align-items: center;
  width: 140px;
  margin-right: 20px;
`;

export const CategoryIconContainer = styled.View<{ active?: boolean }>`
  width: 100px;
  height: 100px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 15px;
  border: ${props => props.active ? `4px solid ${props.theme.primaryColor}` : '3px solid #e9ecef'};
  elevation: 9;
`;

export const CategoryImage = styled.Image`
  width: 100%;
  height: 100%;
  resize-mode: cover;
`;

export const CategoryText = styled.Text`
  font-size: 18px;
  color: #333333;
  text-align: center;
  font-weight: 600;
`;

export const BannerContainer = styled.View`
  height: 200px;
  position: relative;
  margin-bottom: 36px;
`;

export const BannerSlide = styled.ImageBackground`
  width: ${screenWidth}px;
  height: 200px;
  justify-content: center;
  align-items: center;
  position: relative;
`;

export const BannerOverlay = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
`;

export const BannerContent = styled.View`
  align-items: center;
  z-index: 1;
`;

export const BannerTitle = styled.Text`
  color: white;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
`;

export const BannerSubtitle = styled.Text`
  color: white;
  font-size: 18px;
  opacity: 0.9;
  text-align: center;
`;

export const BannerControls = styled.View`
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  flex-direction: row;
  justify-content: center;
  align-items: center;
`;

export const BannerDot = styled.TouchableOpacity<{ active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: ${props => props.active ? 'white' : 'rgba(255, 255, 255, 0.5)'};
  margin: 0 4px;
`;

export const SectionTitle = styled.Text`
  font-size: 32px;
  font-weight: bold;
  color: #333333;
  margin: 0 40px 30px 40px;
`;

export const FoodGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 20px;
`;

export const FoodItem = styled(TouchableOpacity)`
  background-color: #ffffff;
  border-radius: 20px;
  padding: 20px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.15;
  shadow-radius: 8px;
  elevation: 8;
  min-height: 320px;
`;

export const FoodImage = styled.View`
  width: 100%;
  height: 180px;
  background-color: #f5f5f5;
  border-radius: 15px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
`;

export const FoodItemContent = styled.View`
  flex: 1;
  justify-content: space-between;
`;

export const FoodName = styled.Text`
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  text-align: center;
  line-height: 24px;
  margin-bottom: 8px;
`;

export const FoodPrice = styled.Text`
  font-size: 22px;
  font-weight: bold;
  color: ${props => props.theme.primaryColor};
  text-align: center;
  margin-bottom: 15px;
`;

export const AddToCartButton = styled(TouchableOpacity)`
  background-color: ${props => props.theme.primaryColor};
  padding: 16px 20px;
  border-radius: 12px;
  align-items: center;
  margin-top: auto;
`;

export const AddToCartText = styled.Text`
  color: white;
  font-size: 18px;
  font-weight: bold;
`;

export const PopularBadge = styled.View`
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: #FF5722;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  z-index: 1;
`;

export const BadgeText = styled.Text`
  color: white;
  font-size: 18px;
  font-weight: bold;
`;
