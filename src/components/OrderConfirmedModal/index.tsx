import { Modal } from 'react-native';

import React from 'react';

import { Text } from '../Text';
import { Container, OkButton } from './styles';
import { CheckCircle } from 'lucide-react-native';

interface OrderConfirmedModalProps {
  visible: boolean;
  onOk: () => void
}

export function OrderConfirmedModal({ visible, onOk }: OrderConfirmedModalProps) {
  return(
    <Modal
      visible={visible}
      animationType="fade"
    >
      <Container>
        <CheckCircle size={48} color="#fff" />
        <Text weight="600" size={20} color="#fff" style={{ marginTop: 12 }}>Pedido Confirmado</Text>
        <Text color="#fff" opacity={0.9} style={{ marginTop: 4 }}>O pedido já entrou em produção</Text>
        <OkButton onPress={onOk}>
          <Text color="#d73035" weight="600">Ok</Text>
        </OkButton>
      </Container>
    </Modal>
  );
}
