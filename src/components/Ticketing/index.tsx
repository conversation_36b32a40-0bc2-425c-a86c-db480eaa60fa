import { useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, ShoppingCart } from 'lucide-react-native';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Animated, Dimensions, FlatList, ScrollView } from 'react-native';
import { useCreateOrder } from '../../hooks/order/useCreateOrder';
import { useCreateTicketeiraTransaction } from '../../hooks/transaction/useCreateTicketeiraTransaction';
import { useUpdateTransactionStatus } from '../../hooks/transaction/useUpdateTransactionStatus';
import { useCategories } from '../../hooks/useCategories';
import { Contract } from '../../hooks/useContract';
import { useProducts } from '../../hooks/useProducts';
import { useStockManager } from '../../hooks/useStockManager';
import { ICartItem } from '../../types/CartItem';
import { Category } from '../../types/Category';
import { ServicesTypeEnum } from '../../types/enums/services-type.enum';
import { Product } from '../../types/Product';
import { formatCurrency } from '../../utils/formatCurrency';
import LoadingTicketeira from '../Loading/ticketeira';
import { InstallmentOption, PaymentMethod, PaymentMethodDrawer } from '../PaymentMethodModal';
import { CardPaymentScreen } from '../PaymentMethodModal/CardPaymentMethod';
import CartModal from './cart';
import ProductDetailsModal from './productDetails';
import {
  AddToCartButton,
  AddToCartText,
  BackButton,
  BannerContainer,
  BannerContent,
  BannerControls,
  BannerDot,
  BannerOverlay,
  BannerSlide,
  BannerSubtitle,
  BannerTitle,
  CartButton,
  CartCount,
  CategoryContainer,
  CategoryIconContainer,
  CategoryImage,
  CategoryItem,
  CategoryListContainer,
  CategoryText,
  Container,
  FoodGrid,
  FoodImage,
  FoodItem,
  FoodItemContent,
  FoodName,
  FoodPrice,
  Header,
  SectionTitle,
  SubtitleText,
  WelcomeContainer,
  WelcomeText,
} from './styles';
import PixQrcodePaymentScreen from '../PaymentMethodModal/PixQrcodeMethod';
import { useQrCodePix } from '../../hooks/pix/useQrCodePix';

const {width: screenWidth} = Dimensions.get('window');

interface Banner {
  id: number;
  title: string;
  subtitle: string;
  image: string;
}

interface ITicketingProps {
  contract?: Contract;
  onBack: () => void;
}

type ScreenState = 'TICKETEIRA' | 'PIX' | 'CARD' | 'SUCCESS';

export default function Ticketing({onBack}: ITicketingProps) {
  const queryClient = useQueryClient();

  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hasLoadedInitialData, setHasLoadedInitialData] = useState(false);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [isProductDetailsVisible, setIsProductDetailsVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [cartItems, setCartItems] = useState<ICartItem[]>([]);

  const [isPaymentMethodModalOpen, setIsPaymentMethodModalOpen] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<ScreenState>('TICKETEIRA');
  const [transactionId, setTransactionId] = useState<string>('');
  const [installments, setInstallments] = useState<InstallmentOption['installment_qty']>(0);
  const [pixQrCodeData, setPixQrCodeData] = useState<string>('');
  const [cartTotalWithFees, setCartTotalWithFees] = useState(0);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: categories = [],
    isFetching: isLoadingCategories,
    isInitialLoading: isCategoriesInitialLoading,
  } = useCategories({
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    staleTime: 15000,
    cacheTime: 300000,
  });

  const {
    data: apiCatalog = [],
    isLoading: isLoadingProducts,
    // isFetching: isFetchingProducts,
    isInitialLoading: isProductsInitialLoading,
  } = useProducts(selectedCategory === 'all' ? undefined : selectedCategory, {
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    staleTime: 15000,
    cacheTime: 300000,
  });

  const {catalog, hasStock, getAvailableStock} = useStockManager(
    apiCatalog,
    cartItems,
  );

  const createTicketeiraTransaction = useCreateTicketeiraTransaction();
  const updateTransactionStatus = useUpdateTransactionStatus();
  const qrCodePix = useQrCodePix();
  const createOrder = useCreateOrder();

  useEffect(() => {
    if (
      !isCategoriesInitialLoading &&
      !isProductsInitialLoading &&
      !hasLoadedInitialData
    ) {
      setHasLoadedInitialData(true);
    }
  }, [
    isCategoriesInitialLoading,
    isProductsInitialLoading,
    hasLoadedInitialData,
  ]);

  const isInitialLoading = () => {
    return (
      !hasLoadedInitialData &&
      (isCategoriesInitialLoading || isProductsInitialLoading)
    );
  };

  const isLoadingOnlyCategories = () => {
    return hasLoadedInitialData && isLoadingCategories && !isLoadingProducts;
  };

  const isLoadingOnlyProducts = () => {
    return hasLoadedInitialData && isLoadingProducts;
  };

  const animatedItemsRef = useRef<{[key: string]: Animated.Value}>({});
  const getOrCreateAnimatedValue = (itemId: string): Animated.Value => {
    if (!animatedItemsRef.current[itemId]) {
      animatedItemsRef.current[itemId] = new Animated.Value(1);
    }
    return animatedItemsRef.current[itemId];
  };

  const banners: Banner[] = [
    {
      id: 1,
      title: 'Promoção Especial!',
      subtitle: '50% OFF em todos os produtos da padaria',
      image:
        'https://s3.pagway.com.br/totem/products/2025-7-4-b0d9275f-a24e-4352-8cea-125bc62f4f35.jpg',
    },
    {
      id: 2,
      title: 'Novidades Chegando!',
      subtitle: 'Experimente nossos novos sabores',
      image:
        'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=800&h=300&fit=crop',
    },
    {
      id: 3,
      title: 'Combo Família',
      subtitle: 'Economize comprando em família',
      image:
        'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=300&fit=crop',
    },
  ];

  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => {
        const nextSlide = (prev + 1) % banners.length;
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({
            x: nextSlide * screenWidth,
            animated: true,
          });
        }
        return nextSlide;
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [banners.length]);

  function handleBannerScroll(event: any) {
    const slideIndex = Math.round(
      event.nativeEvent.contentOffset.x / screenWidth,
    );
    setCurrentSlide(slideIndex);
  }

  const getCartItemCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const handleProductPress = (product: Product) => {
    setSelectedProduct(product);
    setIsProductDetailsVisible(true);
  };

  const addToCart = async (product: Product, quantity: number = 1) => {
    if (!hasStock(product.Product.id, quantity)) {
      Alert.alert(
        'Erro',
        `Estoque insuficiente. Disponível: ${getAvailableStock(
          product.Product.id,
        )}`,
      );
      return;
    }

    animateAddToCart(product.Product.id);

    const existingItem = cartItems.find(
      item => item.Product.id === product.Product.id,
    );

    if (existingItem) {
      setCartItems(items =>
        items.map(item =>
          item.Product.id === product.Product.id
            ? {...item, quantity: item.quantity + quantity}
            : item,
        ),
      );
    } else {
      const newItem: Product = {
        Product: product.Product,
        quantity: quantity,
      };
      setCartItems([...cartItems, newItem]);
    }
  };

  const animateAddToCart = (itemId: string) => {
    const animation = getOrCreateAnimatedValue(itemId);
    if (animation) {
      Animated.sequence([
        Animated.timing(animation, {
          toValue: 0.97,
          duration: 50,
          useNativeDriver: true,
        }),
        Animated.timing(animation, {
          toValue: 1.05,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(animation, {
          toValue: 1,
          duration: 50,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const updateCartQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    const currentCartItem = cartItems.find(item => item.Product.id === itemId);
    const currentQuantityInCart = currentCartItem
      ? currentCartItem.quantity
      : 0;

    const difference = newQuantity - currentQuantityInCart;

    if (difference > 0) {
      const availableStock = getAvailableStock(itemId);
      if (difference > availableStock) {
        Alert.alert(
          'Erro',
          `Não possui estoque suficiente. Disponível: ${
            availableStock + currentQuantityInCart
          }`,
        );
        return;
      }
    }

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.Product.id === itemId ? {...item, quantity: newQuantity} : item,
      ),
    );
  };

  const removeFromCart = (itemId: string) => {
    setCartItems(prevItems =>
      prevItems.filter(item => item.Product.id !== itemId),
    );
  };

  const handleCheckout = () => {
    setIsCartVisible(false);
    setIsPaymentMethodModalOpen(true);
  };

  const getTotalCartValue = () => {
    return cartItems.reduce((total, item) => {
      return total + parseFloat(item.Product.price) * item.quantity;
    }, 0);
  };

  const resetPaymentStates = () => {
    setCartTotalWithFees(0);
    setTransactionId('');
    setInstallments(0);
    setPixQrCodeData('');
    setCurrentScreen('TICKETEIRA');
};

  const handlePaymentSelected = async (
    paymentMethod: PaymentMethod,
    installment?: InstallmentOption,
  ) => {
      setIsSubmitting(true);
      setIsCartVisible(false);
      setIsPaymentMethodModalOpen(false);
      setInstallments(installment!.installment_qty);

      console.log('Iniciando transação com método:', paymentMethod);
      console.log('Valor total do carrinho:', getTotalCartValue());
      console.log('Quantidade de parcelas:', installment?.installment_qty);

      try {
        const res = await createTicketeiraTransaction.mutateAsync({
          amount: getTotalCartValue(),
          installment_qty: installment!.installment_qty,
          products: cartItems.map(cartItem => ({
            id: cartItem.Product.id,
            quantity: cartItem.quantity,
          })),
          payment_method: paymentMethod,
          payment_reference: 'referencia Ticketeira Payment',
          payment_status: 'PENDING',
          status: 'PENDING',
        });

        setTransactionId(res.transaction.id);

        setCartTotalWithFees(parseFloat(res.transaction.total_amount));

        if (paymentMethod === 'PIX') {
          try {
            await qrCodePix.mutateAsync({
            transactionId: res.transaction.id,
          }).then(response => setPixQrCodeData(response.copiaCola));
          setCurrentScreen('PIX');
          } catch (error) {
            console.error('Erro ao gerar QR Code:', error);
            Alert.alert(
              'Erro',
              'Não foi possível gerar o QR Code PIX. Tente novamente mais tarde.',
            );
          }
        } else if (paymentMethod === 'CARD') {
          setCurrentScreen('CARD');
        }
      } catch (err: any) {
        Alert.alert(
          'Erro',
          err.message ? err.message : 'Não foi possível realizar a transação',
        );
        setIsSubmitting(true);
        resetPaymentStates();
      }
  };

  const handlePaymentSuccess = async () => {
    setIsSubmitting(true);
    try {
      await updateTransactionStatus.mutateAsync({
        transactionId,
        products: cartItems.map(cartItem => ({
          id: cartItem.Product.id,
          quantity: cartItem.quantity,
        })),
        payment_status: 'PAID',
        status: 'COMPLETED',
        payment_reference: 'referencia Ticketeira Payment',
      });

      await createOrder.mutateAsync({
        products: cartItems.map(cartItem => ({
          id: cartItem.Product.id,
          quantity: cartItem.quantity,
        })),
        status: 'COMPLETED',
        transactionId,
      });

      setCartItems([]);
      setIsSubmitting(false);
      setIsCartVisible(false);
      queryClient.invalidateQueries({queryKey: ['products', 'all']});
      Alert.alert('Sucesso', 'Pagamento realizado com sucesso!');
    } catch (err: any) {
      Alert.alert(
        'Erro',
        err.message ? err.message : 'Não foi possível completar a transação',
      );
      setIsSubmitting(false);
    }
  };

  const handlePaymentCancelled = async () => {
    try {
      setIsSubmitting(true);
      await updateTransactionStatus.mutateAsync({
        transactionId,
        products: cartItems.map(cartItem => ({
          id: cartItem.Product.id,
          quantity: cartItem.quantity,
        })),
        payment_status: 'CANCELLED',
        status: 'CANCELLED',
        payment_reference: 'referencia Ticketeira Payment',
      });

      queryClient.invalidateQueries({queryKey: ['products', 'all']});
      setIsSubmitting(false);
      setIsCartVisible(true);
      resetPaymentStates();
    } catch (err: any) {
      Alert.alert(
        'Erro',
        err.message ? err.message : 'Não foi possível completar o cancelamento',
      );
      setIsSubmitting(false);
    }
  };

  const renderCategoryItem = ({item}: {item: Category}) => (
    <CategoryItem
      onPress={() => {
        if (selectedCategory === item.id) {
          setSelectedCategory('all');
        } else {
          setSelectedCategory(item.id);
        }
      }}>
      <CategoryIconContainer active={selectedCategory === item.id}>
        <CategoryImage source={{uri: item.image_url}} />
      </CategoryIconContainer>
      <CategoryText>{item.name}</CategoryText>
    </CategoryItem>
  );

  // telas de pagamento
  if (currentScreen === 'PIX') {
    return (
      <PixQrcodePaymentScreen
        qrCodeData={pixQrCodeData}
        onExpired={handlePaymentCancelled}
        onBack={() => setCurrentScreen('TICKETEIRA')}
        onPaymentReceived={handlePaymentSuccess}
        amount={getTotalCartValue()}
        transactionId={transactionId}
      />
    );
  }

  if (currentScreen === 'CARD') {
    return (
      <CardPaymentScreen
        onBack={() => {
          setCurrentScreen('TICKETEIRA');
          setIsCartVisible(false);
        }}
        onCancel={handlePaymentCancelled}
        onPaymentSuccess={handlePaymentSuccess}
        amount={cartTotalWithFees}
        installments_qty={installments}
        transactionId={transactionId}
      />
    );
  }

  if (isInitialLoading()) {
    return (
      <Container>
        <LoadingTicketeira
          showHeader={true}
          showCategories={true}
          showBanner={true}
          showProducts={true}
          categoriesCount={5}
          productsCount={6}
        />
      </Container>
    );
  }

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Header>
          <BackButton onPress={onBack}>
            <ArrowLeft size={56} color={'#333333'} strokeWidth={2.5} />
          </BackButton>
          <WelcomeContainer>
            <WelcomeText>Bem Vindo(a),</WelcomeText>
            <SubtitleText>
              Escaneie e escolha uma categoria para explorar o menu.
            </SubtitleText>
          </WelcomeContainer>
          <CartButton onPress={() => setIsCartVisible(true)}>
            <ShoppingCart size={42} color="#333333" />
            {getCartItemCount() > 0 && (
              <CartCount>{getCartItemCount()}</CartCount>
            )}
          </CartButton>
        </Header>

        {isLoadingOnlyCategories() ? (
          <LoadingTicketeira
            showHeader={false}
            showCategories={true}
            showBanner={false}
            showProducts={false}
            categoriesCount={5}
          />
        ) : (
          <CategoryContainer>
            <CategoryListContainer>
              <FlatList<Category>
                data={categories}
                renderItem={renderCategoryItem}
                keyExtractor={item => item.id}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{paddingRight: 30}}
              />
            </CategoryListContainer>
          </CategoryContainer>
        )}

        <BannerContainer>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={handleBannerScroll}>
            {banners.map(banner => (
              <BannerSlide
                key={banner.id}
                source={{
                  uri: banner.image,
                }}>
                <BannerOverlay />
                <BannerContent>
                  <BannerTitle>{banner.title}</BannerTitle>
                  <BannerSubtitle>{banner.subtitle}</BannerSubtitle>
                </BannerContent>
              </BannerSlide>
            ))}
          </ScrollView>
          <BannerControls>
            {banners.map((_, index) => (
              <BannerDot
                key={index}
                active={index === currentSlide}
                onPress={() => {
                  setCurrentSlide(index);
                  scrollViewRef.current?.scrollTo({
                    x: index * screenWidth,
                    animated: true,
                  });
                }}
              />
            ))}
          </BannerControls>
        </BannerContainer>

        <SectionTitle>Catálogo:</SectionTitle>

        {isLoadingOnlyProducts() ? (
          <LoadingTicketeira
            showHeader={false}
            showCategories={false}
            showBanner={false}
            showProducts={true}
            productsCount={6}
          />
        ) : (
          <FoodGrid>
            {catalog.map(item => (
              <Animated.View
                key={item.Product.id}
                style={{
                  transform: [
                    {scale: getOrCreateAnimatedValue(item.Product.id)},
                  ],
                  width: '48%',
                  margin: '1%',
                }}>
                <FoodItem onPress={() => handleProductPress(item)}>
                  <FoodImage>
                    <CategoryImage source={{uri: item.Product.image_url}} />
                  </FoodImage>
                  <FoodItemContent>
                    <FoodName>{item.Product.name}</FoodName>
                    <FoodPrice>
                      {formatCurrency(parseFloat(item.Product.price) / 100)}
                    </FoodPrice>
                    <AddToCartButton
                      onPress={e => {
                        e.stopPropagation();
                        addToCart(item);
                      }}>
                      <AddToCartText>Adicionar</AddToCartText>
                    </AddToCartButton>
                  </FoodItemContent>
                </FoodItem>
              </Animated.View>
            ))}
          </FoodGrid>
        )}
      </ScrollView>

      <CartModal
        visible={isCartVisible}
        isDisabled={isSubmitting}
        cartItems={cartItems}
        onClose={() => setIsCartVisible(false)}
        onUpdateQuantity={updateCartQuantity}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
      />

      <ProductDetailsModal
        visible={isProductDetailsVisible}
        product={selectedProduct}
        onClose={() => {
          setIsProductDetailsVisible(false);
          setSelectedProduct(null);
        }}
        onAddToCart={addToCart}
      />

      <PaymentMethodDrawer
        visible={isPaymentMethodModalOpen}
        onClose={() => {
          setIsPaymentMethodModalOpen(false);
          setIsCartVisible(true);
        }}
        onPaymentSelect={handlePaymentSelected}
        amount={getTotalCartValue()}
        service_type={ServicesTypeEnum.TICKETEIRA}
        title="Selecione como pagar"
        pixLabel="PIX"
        cardLabel="Cartão de Crédito"
        cancelLabel="Voltar"
        animationType="slide"
      />
    </Container>
  );
}
