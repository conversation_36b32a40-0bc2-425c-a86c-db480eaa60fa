import type React from 'react';

import { <PERSON><PERSON><PERSON><PERSON>cle, <PERSON>ert<PERSON>riangle, CheckCircle2, Clock, HelpCircle, QrCode } from 'lucide-react-native';
import { useEffect, useState, useRef } from 'react';
import QRCode from 'react-native-qrcode-svg';
import { useAppTheme } from '../../../contexts/ThemeContext';
import { formatCurrency } from '../../../utils/formatCurrency';
import { Button } from '../../Button';
import { Text } from '../../Text';
import {
  AmountContainer,
  AmountLabel,
  AmountValue,
  CancelButtonText,
  CancelContainer,
  Container,
  ExpiredContainer,
  ExpiredText,
  ExpiredTitle,
  FooterContainer,
  Header,
  HelpContainer,
  HelpText,
  InstructionItem,
  InstructionNumber,
  InstructionNumberText,
  InstructionsContainer,
  InstructionText,
  PaymentCard,
  QRContainer,
  StatusContainer,
  StatusText,
  Subtitle,
  SuccessContainer,
  SuccessDetailItem,
  SuccessDetailLabel,
  SuccessDetails,
  SuccessDetailValue,
  SuccessText,
  SuccessTitle,
  TimerContainer,
  TimerText,
  Title,
} from './styles';
import { api } from '../../../utils/api';

interface QRPaymentScreenProps {
  amount: number
  qrCodeData: string
  transactionId: string,
  expirationTime?: number
  onBack: () => void
  onExpired: () => void
  onPaymentReceived?: () => void
}

interface PixStatusResponse {
  situacao: string;
}

export default function PixQrcodePaymentScreen({
  amount,
  qrCodeData,
  transactionId,
  expirationTime = 300,
  onBack,
  onExpired,
  onPaymentReceived,
}: QRPaymentScreenProps) {
  const [timeLeft, setTimeLeft] = useState(expirationTime);
  const [isExpired, setIsExpired] = useState(false);
  const [isPaymentReceived, setIsPaymentReceived] = useState(false);
  const [transactionData, setTransactionData] = useState<{
    amount: number;
    dateTime: string;
  } | null>(null);

  const { theme } = useAppTheme();

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  const checkPixStatus = async () => {
    try {
      const response = await api.get<PixStatusResponse>(`/transaction/${transactionId}/pix-result`);

      if (response.data.situacao === 'Liquidado') {
        const dateTime = new Date().toLocaleString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });

        setTransactionData({ amount, dateTime });
        setIsPaymentReceived(true);
        onPaymentReceived?.();

        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
        }
      }
    } catch (error) {
      console.error('Erro ao verificar status do PIX:', error);
    }
  };

  useEffect(() => {
    if (isPaymentReceived || isExpired) {
      return;
    }

    if (timeLeft <= 0) {
      setIsExpired(true);
      onExpired?.();

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
      return;
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [timeLeft, isPaymentReceived, isExpired, onExpired]);

  useEffect(() => {
    if (isPaymentReceived || isExpired) {
      return;
    }

    checkPixStatus();

    pollingRef.current = setInterval(() => {
      checkPixStatus();
    }, 3000);

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [transactionId, isPaymentReceived, isExpired]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isUrgent = timeLeft <= 60;

  const instructions = [
    'Abra o aplicativo do seu banco',
    'Selecione a opção PIX',
    'Escaneie o QR Code acima',
    'Confirme o pagamento',
  ];

  if (isPaymentReceived && transactionData) {
    return (
      <Container>
        <PaymentCard>
          <SuccessContainer>
            <CheckCircle2 size={120} color={theme.primaryColor} />
            <SuccessTitle>Pagamento Confirmado!</SuccessTitle>
            <SuccessText>Sua transação foi processada com sucesso.</SuccessText>

            <SuccessDetails>
              <SuccessDetailItem>
                <SuccessDetailLabel>Valor Pago:</SuccessDetailLabel>
                <SuccessDetailValue>{formatCurrency(transactionData.amount / 100)}</SuccessDetailValue>
              </SuccessDetailItem>

              <SuccessDetailItem>
                <SuccessDetailLabel>Data/Hora:</SuccessDetailLabel>
                <SuccessDetailValue>{transactionData.dateTime}</SuccessDetailValue>
              </SuccessDetailItem>

              <SuccessDetailItem>
                <SuccessDetailLabel>ID Transação:</SuccessDetailLabel>
                <SuccessDetailValue>{transactionId}</SuccessDetailValue>
              </SuccessDetailItem>
            </SuccessDetails>

            <CancelContainer>
              <Button
                onPress={onBack}
                variant="solid"
                borderRadius={12}
                width={250}
                minHeight={70}
                backgroundColor={theme.primaryColor}
              >
                <Text color="#fff" weight="600" size={24}>Continuar</Text>
              </Button>
            </CancelContainer>
          </SuccessContainer>
        </PaymentCard>
      </Container>
    );
  }

  if (isExpired) {
    return (
      <Container>
        <PaymentCard>
          <ExpiredContainer>
            <AlertCircle size={120} color="#dc2626" />
            <ExpiredTitle>QR Code Expirado</ExpiredTitle>
            <ExpiredText>O tempo para pagamento expirou. Por favor, gere um novo QR Code.</ExpiredText>

            <CancelContainer>
              <Button onPress={onBack} variant="outline" borderRadius={10} borderColor="#dc2626">
                <CancelButtonText style={{ color: '#dc2626' }}>Voltar</CancelButtonText>
              </Button>
            </CancelContainer>
          </ExpiredContainer>
        </PaymentCard>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Pagamento via PIX</Title>
        <Subtitle>Escaneie o QR Code para pagar</Subtitle>
      </Header>

      <PaymentCard>
        <AmountContainer>
          <AmountLabel>Valor a pagar</AmountLabel>
          <AmountValue>{formatCurrency(amount / 100)}</AmountValue>
        </AmountContainer>

        <QRContainer>
          <QRCode
            value={qrCodeData}
            size={220}
            backgroundColor="white"
            color="black"
            logoSize={40}
            logoMargin={8}
            logoBorderRadius={8}
          />
        </QRContainer>

        <TimerContainer urgent={isUrgent}>
          {isUrgent ? <AlertTriangle size={32} color="#dc2626" /> : <Clock size={32} color={theme.primaryColor} />}
          <TimerText urgent={isUrgent}>{formatTime(timeLeft)}</TimerText>
        </TimerContainer>

        <InstructionsContainer>
          {instructions.map((instruction, index) => (
            <InstructionItem key={index}>
              <InstructionNumber>
                <InstructionNumberText>{index + 1}</InstructionNumberText>
              </InstructionNumber>
              <InstructionText>{instruction}</InstructionText>
            </InstructionItem>
          ))}
        </InstructionsContainer>

        <StatusContainer>
          <QrCode size={24} color={theme.primaryColor} />
          <StatusText>Aguardando pagamento...</StatusText>
        </StatusContainer>

        <CancelContainer>
          <Button onPress={onExpired} variant="outline" borderRadius={12} borderColor="#ef4444">
            <CancelButtonText>Cancelar Transação</CancelButtonText>
          </Button>
        </CancelContainer>
      </PaymentCard>

      <FooterContainer>
        <HelpContainer>
          <HelpCircle size={24} color="#64748b" />
          <HelpText>Precisa de ajuda? Chame um atendente ou use o telefone de suporte</HelpText>
        </HelpContainer>
      </FooterContainer>
    </Container>
  );
}
