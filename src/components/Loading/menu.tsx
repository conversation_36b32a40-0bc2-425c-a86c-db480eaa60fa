import type React from 'react';
import { useEffect, useRef } from 'react';
import { Animated, ScrollView } from 'react-native';
import styled from 'styled-components/native';

export const LoadingContainer = styled.View<{ theme: MenuLoadingProps['theme'] }>`
  flex: 1;
  background-color: ${(props) => props.theme?.backgroundColor || '#ffffff'};
`;

export const LoadingHeader = styled(Animated.View)`
  padding: 48px;
  padding-top: 80px;
  padding-bottom: 64px;
  position: relative;
  overflow: hidden;
  min-height: 180px;
  background-color: #f0f0f0;
`;

export const LoadingWelcomeContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
`;

export const LoadingDecorativeCircle = styled(Animated.View)`
  position: absolute;
  top: -100px;
  right: -100px;
  width: 400px;
  height: 400px;
  background-color: #e8e8e8;
  border-radius: 200px;
  z-index: 0;
`;

export const LoadingUserInfo = styled.View`
  flex: 1;
  margin-right: 24px;
  justify-content: center;
`;

export const LoadingWelcomeText = styled(Animated.View)`
  height: 28px;
  width: 120px;
  border-radius: 14px;
  background-color: #e0e0e0;
  margin-bottom: 8px;
`;

export const LoadingUserName = styled(Animated.View)`
  height: 32px;
  width: 180px;
  border-radius: 16px;
  background-color: #e0e0e0;
  margin-bottom: 4px;
`;

export const LoadingSubText = styled(Animated.View)`
  height: 18px;
  width: 140px;
  border-radius: 9px;
  background-color: #e0e0e0;
`;

export const LoadingAvatar = styled(Animated.View)`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #e0e0e0;
  shadow-color: #000;
  shadow-offset: 0px 6px;
  shadow-opacity: 0.15;
  shadow-radius: 12px;
  elevation: 6;
`;

export const LoadingContent = styled(ScrollView)`
  flex: 1;
  padding: 32px;
`;

export const LoadingMenuGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: space-between;
`;

export const LoadingMenuCard = styled.View`
  background-color: ${(props) => props.theme?.backgroundColor || '#ffffff'};
  border-radius: 20px;
  padding: 28px;
  width: 48%;
  min-height: 200px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.12;
  shadow-radius: 12px;
  elevation: 6;
  border: 1px solid #f0f0f0;
  position: relative;
`;

export const LoadingIconContainer = styled(Animated.View)`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #f5f5f5;
  margin-bottom: 16px;
`;

export const LoadingMenuContent = styled.View`
  flex: 1;
  margin-bottom: 16px;
`;

export const LoadingMenuTitle = styled(Animated.View)`
  height: 22px;
  width: 90%;
  border-radius: 11px;
  background-color: #f0f0f0;
  margin-bottom: 12px;
`;

export const LoadingMenuDescription = styled(Animated.View)`
  height: 16px;
  width: 100%;
  border-radius: 8px;
  background-color: #f0f0f0;
  margin-bottom: 8px;
`;

export const LoadingMenuDescriptionShort = styled(Animated.View)`
  height: 16px;
  width: 70%;
  border-radius: 8px;
  background-color: #f0f0f0;
`;

interface MenuLoadingProps {
  theme: {
    backgroundColor: string
    primaryColor: string
    textColor: string
    secondaryColor: string
    accentColor: string
  }
  menuItemsCount?: number
}

export default function MenuLoading({
  theme = {
    backgroundColor: '#ffffff',
    primaryColor: '#4CAF50',
    textColor: '#333333',
    secondaryColor: '#e0e0e0',
    accentColor: '#FF5722',
  },
  menuItemsCount = 6,
}: MenuLoadingProps) {
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const headerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: false,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0,
          duration: 1200,
          useNativeDriver: false,
        }),
      ]).start(() => pulse());
    };

    const headerPulse = () => {
      Animated.sequence([
        Animated.timing(headerAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: false,
        }),
        Animated.timing(headerAnim, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: false,
        }),
      ]).start(() => headerPulse());
    };

    pulse();
    headerPulse();
  }, [pulseAnim, headerAnim]);

  const animatedStyle = {
    opacity: pulseAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.8],
    }),
  };

  const headerAnimatedStyle = {
    opacity: headerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.4, 0.9],
    }),
  };

  const renderMenuCards = () => {
    return Array.from({ length: menuItemsCount }).map((_, index) => (
      <LoadingMenuCard key={index} theme={theme}>
        <LoadingIconContainer style={animatedStyle} />
        <LoadingMenuContent>
          <LoadingMenuTitle style={animatedStyle} />
          <LoadingMenuDescription style={animatedStyle} />
          <LoadingMenuDescriptionShort style={animatedStyle} />
        </LoadingMenuContent>
      </LoadingMenuCard>
    ));
  };

  return (
    <LoadingContainer theme={theme}>
      {/* Header Loading */}
      <LoadingHeader style={headerAnimatedStyle}>
        <LoadingDecorativeCircle style={headerAnimatedStyle} />
        <LoadingWelcomeContainer>
          <LoadingUserInfo>
            <LoadingWelcomeText style={animatedStyle} />
            <LoadingUserName style={animatedStyle} />
            <LoadingSubText style={animatedStyle} />
          </LoadingUserInfo>
          <LoadingAvatar style={animatedStyle} />
        </LoadingWelcomeContainer>
      </LoadingHeader>

      {/* Content Loading */}
      <LoadingContent showsVerticalScrollIndicator={false}>
        <LoadingMenuGrid>{renderMenuCards()}</LoadingMenuGrid>
      </LoadingContent>
    </LoadingContainer>
  );
}
