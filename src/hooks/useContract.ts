import { useQuery } from '@tanstack/react-query';
import { api } from '../utils/api';

export interface Contract {
  id: string
  start_date: any
  end_date: any
  amount: string
  ticketeira_enabled: boolean
  veiculares_enabled: boolean
  boletos_enabled: boolean
  franchisor_id: string
  franchise_id: string
  created_at: string
  updated_at: string
  Franchisor: Franchisor
  Franchise: Franchise
  Totems: Totem[]
}

interface Franchisor {
  id: string
  name: string
  email: string
}

interface Franchise {
  id: string
  name: string
  email: string
}

interface Totem {
  id: string
  name: string
  identifier: string
}


export function useContract(serialNumber: string, options = {}) {
  return useQuery({
    queryKey: ['contract'],
    queryFn: async (): Promise<Contract> => {
      const { data } = await api.get(`/totem-contracts/serial-number/${serialNumber}`);
      return data;
    },
    refetchOnWindowFocus: true,
    ...options,
  });
}
