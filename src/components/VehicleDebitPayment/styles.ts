import { TouchableOpacity, View, TextInput } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styled from 'styled-components/native';

interface DebtItemProps {
  selected: boolean;
}

interface SelectButtonProps {
  selected: boolean;
}

interface DebtIconProps {
  backgroundColor: string;
}

interface DebtBadgeProps {
  color: string;
}

export const Container = styled.View`
  flex: 1;
  background-color: #f7fafc;
`;

export const Header = styled(LinearGradient).attrs({
  colors: ['#3182CE', '#2B6CB0'],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 1 },
})`
  padding: 30px;
  padding-top: 80px;
  flex-direction: row;
  align-items: center;
`;

export const BackButton = styled(TouchableOpacity)`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  justify-content: center;
  align-items: center;
  margin-right: 24px;
`;

export const HeaderContent = styled.View`
  flex: 1;
`;

export const Content = styled.View`
  flex: 1;
  padding: 30px;
`;

export const SearchSection = styled.View`
  margin-bottom: 32px;
`;

export const SearchForm = styled.View`
  background-color: #fff;
  border-radius: 20px;
  padding: 30px;
  elevation: 3;
`;

export const InputGroup = styled.View`
  margin-bottom: 24px;
`;

export const InputLabel = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12px;
`;

export const Input = styled(TextInput)`
  border-width: 2px;
  border-color: #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  font-size: 20px;
  background-color: #fff;
  color: #1a202c;
  min-height: 64px;
`;

export const SearchButton = styled(TouchableOpacity)`
  background-color: #3182ce;
  border-radius: 16px;
  padding: 20px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  min-height: 72px;
`;

export const VehicleCard = styled.View`
  background-color: #fff;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 32px;
  elevation: 3;
  border-left-width: 6px;
  border-left-color: #3182ce;
`;

export const VehicleInfo = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 24px;
  gap: 12px;
`;

export const VehicleDetail = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom-width: 1px;
  border-bottom-color: #f7fafc;
`;

export const DebtsContainer = styled.View`
  margin-bottom: 32px;
`;

export const DebtItem = styled.View<DebtItemProps>`
  background-color: #fff;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 20px;
  elevation: 3;
  border-width: 3px;
  border-color: ${props => props.selected ? '#3182CE' : 'transparent'};
`;

export const DebtHeader = styled.View`
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 24px;
`;

export const DebtIcon = styled.View<DebtIconProps>`
  width: 64px;
  height: 64px;
  border-radius: 32px;
  background-color: ${props => props.backgroundColor};
  justify-content: center;
  align-items: center;
  margin-right: 16px;
`;

export const DebtContent = styled.View`
  flex: 1;
`;

export const DebtTitle = styled.View`
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 12px;
  gap: 12px;
`;

export const DebtMeta = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const DebtAmount = styled.View`
  align-items: flex-end;
`;

export const DebtBadge = styled.View<DebtBadgeProps>`
  background-color: ${props => props.color};
  padding: 8px 12px;
  border-radius: 12px;
`;

export const SelectButton = styled(TouchableOpacity)<SelectButtonProps>`
  background-color: ${props => props.selected ? '#3182CE' : 'transparent'};
  border-width: 2px;
  border-color: #3182ce;
  border-radius: 12px;
  padding: 16px 24px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: flex-start;
  min-height: 56px;
`;

export const EmptyState = styled.View`
  background-color: #fff;
  border-radius: 20px;
  padding: 60px 30px;
  align-items: center;
  elevation: 4;
  margin: 60px 0;
`;

export const EmptyIcon = styled.View`
  width: 120px;
  height: 120px;
  border-radius: 60px;
  background-color: #f0fff4;
  justify-content: center;
  align-items: center;
`;

export const FloatingPayment = styled.View`
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-color: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 32px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  elevation: 12;
  border-top-width: 2px;
  border-top-color: #e2e8f0;
  min-height: 100px;
`;

export const PaymentInfo = styled.View`
  flex: 1;
`;

export const PaymentButton = styled(TouchableOpacity)`
  background-color: #38a169;
  border-radius: 16px;
  padding: 18px 28px;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  min-height: 64px;
`;

export const LoadingOverlay = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

// export const Backdrop = styled.View`
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
//   background-color: rgba(0, 0, 0, 0.7);
// `;

export const LoadingContent = styled.View`
  background-color: rgba(26, 32, 44, 0.95);
  border-radius: 20px;
  padding: 40px;
  align-items: center;
  justify-content: center;
  elevation: 20;
  min-width: 280px;
  border-width: 1px;
  border-color: rgba(255, 255, 255, 0.1);
`;
