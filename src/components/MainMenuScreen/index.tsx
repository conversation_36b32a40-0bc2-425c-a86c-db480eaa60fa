import { Car, FileText, Ticket } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';

import DeviceInfo from 'react-native-device-info';

import { useAppTheme } from '../../contexts/ThemeContext';
import { Contract, useContract } from '../../hooks/useContract';
import { Text } from '../Text';
import {
  Avatar,
  Container,
  Content,
  DecorativeCircle,
  Header,
  IconContainer,
  MenuCard,
  MenuContent,
  MenuDescription,
  MenuGrid,
  MenuTitle,
  UserInfo,
  WelcomeContainer,
} from './styles';

interface MainMenuScreenProps {
  userName?: string;
  contract: Contract;
  onTicketingPress: () => void;
  onBillPaymentPress: () => void;
  onVehicleDebtsPress: () => void;
}

interface MenuOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  backgroundColor: string;
  onPress: () => void;
  enabled: boolean;
}

export function MainMenuScreen({
  userName = 'Usuário',
  onTicketingPress,
  onBillPaymentPress,
  onVehicleDebtsPress,
  contract,
}: MainMenuScreenProps) {
  const { theme } = useAppTheme();

  const allMenuOptions: MenuOption[] = [
    {
      id: 'ticketing',
      title: 'Ticketeira',
      description: 'Gerencie tickets, vendas e atendimento ao cliente',
      icon: <Ticket size={48} color="#fff" />,
      backgroundColor: '#4CAF50',
      onPress: onTicketingPress,
      enabled: contract.ticketeira_enabled,
    },
    {
      id: 'bill-payment',
      title: 'Pagamentos de Boletos',
      description: 'Processe pagamentos de boletos e contas diversas',
      icon: <FileText size={48} color="#fff" />,
      backgroundColor: '#2196F3',
      onPress: onBillPaymentPress,
      enabled: contract.boletos_enabled,
    },
    {
      id: 'vehicle-debts',
      title: 'Débitos Veiculares',
      description: 'Consulte e quite débitos de veículos (IPVA, multas, etc.)',
      icon: <Car size={48} color="#fff" />,
      backgroundColor: '#FF9800',
      onPress: onVehicleDebtsPress,
      enabled: contract.veiculares_enabled,
    },
  ];

  const menuOptions: MenuOption[] = allMenuOptions.filter(option => option.enabled);

  return (
    <Container>
      <Header colors={['', '']}>
        <DecorativeCircle />
        <WelcomeContainer>
          <UserInfo>
            <Text size={20} color={theme.textColor} style={{opacity: 0.9}}>
              Olá, {userName} 👋
            </Text>
            <Text size={36} color={theme.textColor} weight="700" style={{marginTop: 8}}>
              O que vamos fazer hoje?
            </Text>
          </UserInfo>
          <Avatar>
            <Text size={28} color={theme.textColor} weight="600">
              {userName.charAt(0).toUpperCase()}
            </Text>
          </Avatar>
        </WelcomeContainer>
      </Header>

      <Content showsVerticalScrollIndicator={false}>
        <MenuGrid>
          {menuOptions.map(option => (
            <MenuCard key={option.id} onPress={option.onPress}>
              <IconContainer backgroundColor={option.backgroundColor}>
                {option.icon}
              </IconContainer>

              <MenuContent>
                <MenuTitle>{option.title}</MenuTitle>
                <MenuDescription>{option.description}</MenuDescription>
              </MenuContent>
            </MenuCard>
          ))}
        </MenuGrid>
      </Content>
    </Container>
  );
}
