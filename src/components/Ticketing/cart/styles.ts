import { Dimensions, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const CartOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
`;

export const CartContainer = styled.View`
  width: ${screenWidth}px;
  min-height: ${screenHeight * 0.7}px;
  background-color: white;
  border-radius: 25px;
  overflow: hidden;
`;


export const CloseButton = styled(TouchableOpacity)`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #ffffff;
  justify-content: center;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

export const CloseButtonText = styled.Text`
  font-size: 28px;
  color: #666666;
  font-weight: bold;
`;

export const CartContent = styled.View`
  flex: 1;
  padding: 30px;
  background-color: #ffffff;
`;

export const CartItem = styled.View`
  flex-direction: row;
  padding: 25px;
  background-color: #ffffff;
  border-radius: 20px;
  margin-bottom: 20px;
  elevation: 3;
`;

export const CartItemImage = styled.Image`
  width: 120px;
  height: 120px;
  border-radius: 20px;
  margin-right: 25px;
  resize-mode: cover;
`;

export const CartItemDetails = styled.View`
  flex: 1;
  justify-content: space-between;
  padding: 5px 0;
`;

export const CartItemInfo = styled.View`
  flex: 1;
`;

export const CartItemName = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10px;
  line-height: 28px;
`;

export const CartItemPrice = styled.Text`
  font-size: 22px;
  color: ${props => props.theme.primaryColor};
  font-weight: bold;
  margin-bottom: 15px;
`;

export const QuantityContainer = styled.View`
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  margin: 15px 0;
`;

export const QuantityButton = styled(TouchableOpacity)`
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #f8f9fa;
  justify-content: center;
  align-items: center;
  border: 2px solid #e9ecef;
`;

export const QuantityButtonText = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: #333333;
`;

export const CartItemQuantity = styled.Text`
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin: 0 25px;
  min-width: 40px;
  text-align: center;
`;

export const RemoveButton = styled(TouchableOpacity)`
  align-self: flex-start;
  padding: 8px 15px;
  border-radius: 10px;
  background-color: #fff5f5;
`;

export const RemoveButtonText = styled.Text`
  font-size: 18px;
  color: #FF5722;
  font-weight: 600;
`;

export const Separator = styled.View`
  height: 2px;
  background-color: #f0f0f0;
  margin: 15px 0;
  border-radius: 1px;
`;

export const EmptyCart = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 60px 40px;
`;

export const EmptyCartText = styled.Text`
  font-size: 28px;
  color: #666666;
  text-align: center;
  font-weight: 600;
  margin-bottom: 10px;
`;

export const EmptyCartSubtext = styled.Text`
  font-size: 20px;
  color: #999999;
  text-align: center;
  line-height: 26px;
`;

export const CartFooter = styled.View`
  padding: 40px;
  background-color: #f8f9fa;
  border-top-width: 2px;
  border-top-color: #e9ecef;
`;

export const TotalContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 2;
`;

export const TotalLabel = styled.Text`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
`;

export const TotalValue = styled.Text`
  font-size: 32px;
  font-weight: bold;
  color: ${props => props.theme.primaryColor};
`;

export const CheckoutButton = styled(TouchableOpacity)`
  background-color: ${props => props.theme.primaryColor};
  padding: 25px;
  opacity: ${props => props.disabled ? 0.5 : 1};
  border-radius: 20px;
  align-items: center;
  elevation: ${props => props.disabled ? 0 : 3};
`;

export const CheckoutButtonText = styled.Text`
  color: white;
  font-size: 24px;
  font-weight: bold;
`;

export const ContinueShoppingButton = styled(TouchableOpacity)`
  background-color: transparent;
  padding: 20px;
  border-radius: 15px;
  align-items: center;
  margin-top: 15px;
  border: 2px solid #e9ecef;
`;

export const ContinueShoppingText = styled.Text`
  color: #666666;
  font-size: 20px;
  font-weight: 600;
`;
