import { useMutation } from '@tanstack/react-query';
import { useCreateTransaction } from './useCreateTransaction';
import { ICreateTransactionRequest, ITransactionResponse } from '../../types/Transaction..types';
import { useDeviceSerial } from '../useDeviceSerial';

export function useCreateVehicularesTransaction() {
  const createTransactionMutation = useCreateTransaction();
  const { serialNumber } = useDeviceSerial();

  return useMutation({
    mutationFn: async ({
      amount,
      installment_qty,
      payment_method,
      payment_reference,
      payment_status = 'PENDING',
      status = 'PENDING',
      billCart,
    }: {
      amount: number;
      installment_qty: number;
      payment_method: 'PIX' | 'CARD';
      payment_reference?: string;
      payment_status?: ICreateTransactionRequest['payment_status'];
      status?: ICreateTransactionRequest['status'];
      billCart: { id: string }[];
    }): Promise<ITransactionResponse> => {
      const transactionData: ICreateTransactionRequest = {
        service_type: 'DEBITOS_VEICULARES',
        installment_qty,
        amount,
        payment_method,
        payment_reference,
        payment_status,
        status,
        deviceSerial: serialNumber!,
        billCart,
      };

      return createTransactionMutation.mutateAsync(transactionData);
    },
  });
}
