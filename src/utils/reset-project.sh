#!/bin/bash
echo "🧹 Resetando projeto React Native..."

pkill -f "metro" 2>/dev/null || true
pkill -f "react-native" 2>/dev/null || true
pkill -f "gradle" 2>/dev/null || true

echo "🔄 Limpando React Native..."
npx react-native clean

# echo "📦 Reinstalando dependências..."
# rm -rf node_modules yarn.lock
# yarn

echo "🔧 Limpando Gradle..."
rm -rf ~/.gradle/caches/
rm -rf ~/.gradle/daemon/
rm -rf android/.gradle/
rm -rf android/app/.cxx/
rm -rf android/app/build/

echo "🚀 Tentando build..."
cd android
./gradlew clean
cd ..

echo "✅ Reset concluído! Agora tente: npx react-native run-android"