export interface IVehicleDebtsSearchParams {
  state: string;
  licensePlate: string;
  renavam: string;
  cpfCnpj: string;
}

export interface IConsultResponse {
  transactionId: number,
  message: string,
  errorCode: string
}

export interface IDebtData {
  id: string;
  type: string;
  year: number;
  title: string;
  amount: number;
  dueDate: string;
  distinct: any[];
  required: boolean;
  dependsOn: string[];
  isExpired: boolean;
  description: string;
  hasDiscount: boolean;
  expirationDate: string;
}

export interface IVehicleDebtsResponse {
  id: string;
  type: string;
  debts_id: string;
  cliente_request_id: string;
  vehicle_uf: string;
  vehicle_document: string;
  vehicle_plate: string;
  vehicle_renavam: string;
  debts_data: IDebtData[];
  created_at: any;
  updated_at: any;
}
