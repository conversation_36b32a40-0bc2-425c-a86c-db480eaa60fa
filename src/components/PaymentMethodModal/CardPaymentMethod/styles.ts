import styled from 'styled-components/native';

export const Container = styled.View`
  flex: 1;
  background-color: #f8fafc;
`;

export const Header = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 60px 24px 24px 24px;
  background-color: #ffffff;
  border-bottom-width: 1px;
  border-bottom-color: #e5e7eb;
`;

export const BackButton = styled.TouchableOpacity`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: transparent;
  align-items: center;
  justify-content: center;
`;

export const HeaderTitle = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: #111827;
`;

export const HeaderSpacer = styled.View`
  width: 40px;
`;

export const Content = styled.View`
  flex: 1;
  padding: 40px 32px 0 32px;
  align-items: center;
`;

export const StatusContainer = styled.View`
  width: 120px;
  height: 120px;
  border-radius: 60px;
  background-color: #ffffff;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 4;
`;

export const ContentContainer = styled.View`
  align-items: center;
  max-width: 600px;
  width: 100%;
`;

export const Title = styled.Text<{ color?: string }>`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.color || '#111827'};
  text-align: center;
  margin-bottom: 16px;
`;

export const Subtitle = styled.Text`
  font-size: 20px;
  color: #6b7280;
  text-align: center;
  margin-bottom: 32px;
  line-height: 28px;
`;

export const AmountContainer = styled.View`
  background-color: #ffffff;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  width: 100%;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 4;
`;

export const AmountLabel = styled.Text`
  font-size: 18px;
  color: #6b7280;
  margin-bottom: 8px;
`;

export const AmountValue = styled.Text`
  font-size: 48px;
  font-weight: 700;
  color: #2563eb;
`;

export const ProcessingText = styled.Text`
  font-size: 16px;
  color: #9ca3af;
  text-align: center;
  margin-top: 16px;
`;

export const TransactionDetails = styled.View`
  background-color: #ffffff;
  border-radius: 16px;
  padding: 24px;
  width: 100%;
  margin-top: 16px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 4;
`;

export const DetailRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 16px;
`;

export const DetailLabel = styled.Text`
  font-size: 18px;
  color: #6b7280;
`;

export const DetailValue = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

export const LoadingContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-top: 24px;
`;

export const LoadingText = styled.Text`
  font-size: 16px;
  color: #6b7280;
  margin-left: 12px;
`;

export const ActionsContainer = styled.View`
  padding: 0 32px 40px 32px;
  gap: 16px;
`;

export const PrimaryButton = styled.TouchableOpacity`
  background-color: #2563eb;
  border-radius: 12px;
  padding: 20px 0;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

export const PrimaryButtonText = styled.Text`
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
`;

export const SecondaryButton = styled.TouchableOpacity`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px 0;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  border-width: 1px;
  border-color: #d1d5db;
  gap: 8px;
`;

export const SecondaryButtonText = styled.Text`
  color: #6b7280;
  font-size: 20px;
  font-weight: 600;
`;
