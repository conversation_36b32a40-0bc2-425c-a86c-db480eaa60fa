import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../utils/api';
import { ICreateOrderRequest, IOrder } from '../../types/Order.types';

export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: ICreateOrderRequest): Promise<IOrder> => {
      if (!orderData.products || orderData.products.length === 0) {
        throw new Error('Lista de produtos não pode estar vazia');
      }

      if (!orderData.transactionId) {
        throw new Error('ID da transação é obrigatório');
      }

      for (const item of orderData.products) {
        if (!item.id) {
          throw new Error('ID do produto é obrigatório');
        }

        if (item.quantity !== undefined && item.quantity <= 0) {
          throw new Error('Quantidade deve ser maior que zero');
        }
      }

      const { data } = await api.post('/order', orderData);

      if (data.success && data.data) {
        return data.data;
      }

      return data;
    },
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ['orders'] });
    },
    onError: (error: any) => {
      console.error('Erro ao criar pedido:', error.message);
    },
  });
}
