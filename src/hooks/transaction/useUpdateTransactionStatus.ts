import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IUpdateTransactionStatusRequest } from '../../types/Transaction..types';
import { api } from '../../utils/api';

export function useUpdateTransactionStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      transactionId,
      ...updateData
    }: {
      transactionId: string;
    } & IUpdateTransactionStatusRequest): Promise<void> => {
      const { data } = await api.put(`/transaction/${transactionId}`, updateData);
      return data;
    },
    onSuccess: (_, variables) => {
      // queryClient.invalidateQueries({ queryKey: ['transactions'] });
      // queryClient.invalidateQueries({ queryKey: ['transaction', variables.transactionId] });
    },
    onError: (error: any) => {
      console.error('Erro ao atualizar status da transação:', error.message);
    },
  });
}
