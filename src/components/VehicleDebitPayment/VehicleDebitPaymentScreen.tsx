import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Car,
  Check,
  CheckCircle2,
  CreditCard,
  Info,
  Loader,
  Search,
} from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native';
import { formatCurrency } from '../../utils/formatCurrency';
import { Text } from '../Text';

import { useAppTheme } from '../../contexts/ThemeContext';
import { useVehiclesDebtSettlement } from '../../hooks/settlement/useVehiclesDebtSettlement';
import { useCreateVehicularesTransaction } from '../../hooks/transaction/useCreateVehicularesTransaction';
import { useUpdateTransactionStatus } from '../../hooks/transaction/useUpdateTransactionStatus';
import { vehicleDebtsService } from '../../services/vehiclesDebts/vehiclesDebtsService';
import { ServicesTypeEnum } from '../../types/enums/services-type.enum';
import { IDebtData, IVehicleDebtsResponse } from '../../types/VehiclesDebts';
import { convertToCents } from '../../utils/convertToCents';
import { formatDateBr } from '../../utils/formatDateBr';
import { formatRenavam } from '../../utils/formatRenavam';
import {
  cleanVehiclePlate,
  formatVehiclePlate,
} from '../../utils/formatVehiclePlate';
import { cleanCpf, formatCpfCnpj } from '../../utils/masks/formatCpfCnpj';
import { InstallmentOption, PaymentMethod, PaymentMethodDrawer } from '../PaymentMethodModal';
import { CardPaymentScreen } from '../PaymentMethodModal/CardPaymentMethod';

import {
  BackButton,
  Container,
  Content,
  DebtAmount,
  DebtBadge,
  DebtContent,
  DebtHeader,
  DebtIcon,
  DebtItem,
  DebtMeta,
  DebtsContainer,
  DebtTitle,
  EmptyIcon,
  EmptyState,
  FloatingPayment,
  Header,
  HeaderContent,
  Input,
  InputGroup,
  InputLabel,
  LoadingContent,
  LoadingOverlay,
  PaymentButton,
  PaymentInfo,
  SearchButton,
  SearchForm,
  SearchSection,
  SelectButton,
  VehicleCard,
  VehicleDetail,
  VehicleInfo,
} from './styles';

interface VehicleInfo {
  plate: string;
  renavam: string;
  document: string;
  uf: string;
}

type PaymentScreen = 'debts' | 'pix' | 'card';

interface VehicleDebtsScreenProps {
  onBack: () => void;
}

export function VehicleDebtsScreen({ onBack }: VehicleDebtsScreenProps) {
  const [plate, setPlate] = useState('IPV2501');
  const [renavam, setRenavam] = useState('67709157143');
  const [vehicleState, setVehicleState] = useState('DF');
  const [cpfCnpj, setCpfCnpj] = useState('81183207077');

  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Processando...');
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo | null>(null);
  const [debts, setDebts] = useState<IDebtData[]>([]);
  const [selectedDebts, setSelectedDebts] = useState<string[]>([]);
  const [isPaymentModalVisible, setIsPaymentModalVisible] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<PaymentScreen>('debts');
  const [transactionId, setTransactionId] = useState('');
  const [installments, setInstallments] = useState(0);
  const [cartTotalWithFees, setCartTotalWithFees] = useState(0);

  const { theme } = useAppTheme();

  const createVehicularesTransaction = useCreateVehicularesTransaction();
  const updateTransactionStatus = useUpdateTransactionStatus();
  const vehiclesDebtSettlement = useVehiclesDebtSettlement();

  const mapDebtType = (type: string): 'ticket' | 'licensing' | 'ipva' => {
    switch (type.toLowerCase()) {
      case 'ticket':
        return 'ticket';
      case 'licensing':
        return 'licensing';
      case 'ipva':
        return 'ipva';
      default:
        return 'ticket';
    }
  };

  const transformApiResponse = useCallback((data: IVehicleDebtsResponse) => ({
    vehicleInfo: {
      plate: data.vehicle_plate,
      renavam: data.vehicle_renavam,
      document: data.vehicle_document,
      uf: data.vehicle_uf,
    },
    debts: data.debts_data.map(debt => ({
      id: debt.id,
      type: mapDebtType(debt.type),
      title: debt.title,
      description: debt.description,
      amount: debt.amount,
      dueDate: formatDateBr(debt.dueDate),
      expirationDate: formatDateBr(debt.expirationDate),
      year: debt.year,
      isExpired: debt.isExpired,
      hasDiscount: debt.hasDiscount,
      required: debt.required,
      dependsOn: debt.dependsOn,
      distinct: debt.distinct,
    })),
  }), []);

  const searchDebts = useCallback(async () => {
    console.log('Searching debts with:');

    if (!plate && !renavam) {
      Alert.alert('Atenção', 'Informe a placa ou RENAVAM do veículo');
      return;
    }

    if (!cpfCnpj || !vehicleState) {
      Alert.alert('Atenção', 'Preencha todos os campos obrigatórios');
      return;
    }

    setIsLoading(true);
    setLoadingMessage('Consultando débitos...');

    try {
      const vehicleData = {
        licensePlate: cleanVehiclePlate(plate),
        renavam: renavam,
        cpfCnpj: cleanCpf(cpfCnpj),
        state: vehicleState,
      };

      const response = await vehicleDebtsService.searchDebts(vehicleData);
      const { vehicleInfo: apiVehicleInfo, debts: apiDebts } = transformApiResponse(response);

      setVehicleInfo(apiVehicleInfo);
      setDebts(apiDebts);
      setSelectedDebts([]);
    } catch (error: any) {
      let errorMessage = 'Ocorreu um erro inesperado';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      Alert.alert('Erro', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [plate, renavam, cpfCnpj, vehicleState, transformApiResponse]);

  const handleSearch = useCallback(() => {
    searchDebts();
  }, [searchDebts]);

  const toggleDebtSelection = useCallback((id: string) => {
    const debt = debts.find(d => d.id === id);
    if (!debt) {return;}

    if (debt.dependsOn.length > 0) {
      const unselectedDeps = debt.dependsOn.filter(
        depId => !selectedDebts.includes(depId),
      );

      if (unselectedDeps.length > 0) {
        const firstUnselectedDep = debts.find(debt => unselectedDeps.includes(debt.id));
        Alert.alert(
          'Dependência necessária',
          `Este débito depende de: ${firstUnselectedDep?.title}`,
        );
        return;
      }
    }

    if (debt.distinct.length > 0) {
      const conflictingDebts = debt.distinct.filter(
        distinctId => selectedDebts.includes(distinctId),
      );
      if (conflictingDebts.length > 0) {
        Alert.alert(
          'Débito incompatível',
          'Este débito não pode ser pago junto com outros selecionados.',
        );
        return;
      }
    }

    setSelectedDebts(prev => {
      if (prev.includes(id)) {
        const dependentIds = debts
          .filter(debt => debt.dependsOn.includes(id))
          .map(debt => debt.id);
        return prev.filter(debtId => debtId !== id && !dependentIds.includes(debtId));
      } else {
        return [...prev, id];
      }
    });
  }, [debts, selectedDebts]);

  const handlePaymentSelected = useCallback(async (
    paymentMethod: PaymentMethod,
    installment?: InstallmentOption
  ) => {
    try {
      setInstallments(installment?.installment_qty || 1);
      setIsLoading(true);
      setLoadingMessage('Criando transação...');

      const response = await createVehicularesTransaction.mutateAsync({
        amount: convertToCents(calculateTotal()),
        installment_qty: installment?.installment_qty ?? 1,
        payment_method: paymentMethod,
        payment_reference: 'payment reference from vehicles',
        payment_status: 'PENDING',
        status: 'PENDING',
        billCart: selectedDebts.map(debt => ({ id: debt })),
      });

      setCartTotalWithFees(parseFloat(response.transaction.total_amount));
      setTransactionId(response.transaction.id);
      setIsPaymentModalVisible(false);
      setIsLoading(false);
      setCurrentScreen(paymentMethod === 'PIX' ? 'pix' : 'card');
    } catch (error: any) {
      setIsLoading(false);
      Alert.alert('Erro', error.message || 'Não foi possível realizar a transação');
    }
  }, [selectedDebts, createVehicularesTransaction]);

  const handlePaymentSuccess = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadingMessage('Finalizando pagamento...');

      await updateTransactionStatus.mutateAsync({
        transactionId,
        products: [],
        payment_status: 'PAID',
        status: 'COMPLETED',
      });

      setLoadingMessage('Liquidando débitos...');
      await vehiclesDebtSettlement.mutateAsync(selectedDebts);


      setIsLoading(false);
      setSelectedDebts([]);
      setDebts([]);
    } catch (error: any) {
      setIsLoading(false);
      Alert.alert('Erro', 'Não foi possível completar o pagamento');
    }
  }, [transactionId, updateTransactionStatus, vehiclesDebtSettlement, selectedDebts]);

  const handlePaymentCancelled = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadingMessage('Cancelando pagamento...');

      await updateTransactionStatus.mutateAsync({
        transactionId,
        products: [],
        payment_status: 'CANCELLED',
        status: 'CANCELLED',
      });

      setIsLoading(false);
      setCurrentScreen('debts');
    } catch (err: any) {
      Alert.alert('Erro', 'Não foi possível cancelar o pagamento');
      setIsLoading(false);
    }
  }, [transactionId, updateTransactionStatus]);

  const calculateTotal = useCallback(() =>
    debts
      .filter(debt => selectedDebts.includes(debt.id))
      .reduce((sum, debt) => sum + debt.amount, 0),
    [debts, selectedDebts]
  );

  const getDebtIcon = useCallback((type: string) => {
    switch (type) {
      case 'ipva':
        return <Car size={32} color="#fff" />;
      case 'licensing':
        return <Info size={32} color="#fff" />;
      case 'ticket':
        return <AlertCircle size={32} color="#fff" />;
      default:
        return <AlertCircle size={32} color="#fff" />;
    }
  }, []);

  const getDebtColor = useCallback((type: string) => {
    switch (type) {
      case 'ipva':
        return '#E53E3E';
      case 'licensing':
        return '#3182CE';
      case 'ticket':
        return '#ED8936';
      default:
        return '#718096';
    }
  }, []);

  if (currentScreen === 'card') {
    return (
      <CardPaymentScreen
        onBack={() => {
          setCurrentScreen('debts');
        }}
        onPaymentSuccess={handlePaymentSuccess}
        amount={400000}
        transactionId={transactionId}
        installments_qty={installments}
        onCancel={handlePaymentCancelled}
      />
    );
  }

  return (
    <Container>
      <Header>
        <BackButton onPress={onBack}>
          <ArrowLeft size={32} color="#fff" />
        </BackButton>
        <HeaderContent>
          <Text size={32} color="#fff" weight="700">
            Débitos Veiculares
          </Text>
          <Text size={18} color="#fff" opacity={0.9}>
            Consulte e pague suas pendências
          </Text>
        </HeaderContent>
      </Header>

      <Content>
        <SearchSection>
          <Text size={28} weight="700" color="#1A202C" style={{ marginBottom: 24 }}>
            Dados do Veículo
          </Text>

          <SearchForm>
            <InputGroup>
              <InputLabel>Placa</InputLabel>
              <Input
                value={plate}
                onChangeText={text => setPlate(formatVehiclePlate(text))}
                placeholder="ABC-1234"
                autoCapitalize="characters"
                maxLength={8}
              />
            </InputGroup>

            <InputGroup>
              <InputLabel>RENAVAM</InputLabel>
              <Input
                value={renavam}
                onChangeText={text => setRenavam(formatRenavam(text))}
                placeholder="12345678901"
                keyboardType="numeric"
                maxLength={11}
              />
            </InputGroup>

            <InputGroup>
              <InputLabel>Estado (UF)</InputLabel>
              <Input
                value={vehicleState}
                onChangeText={text =>
                  setVehicleState(text.replace(/[^A-Za-z]/g, '').toUpperCase())
                }
                placeholder="SP"
                maxLength={2}
                autoCapitalize="characters"
              />
            </InputGroup>

            <InputGroup>
              <InputLabel>CPF/CNPJ do Proprietário</InputLabel>
              <Input
                value={cpfCnpj}
                onChangeText={text => setCpfCnpj(formatCpfCnpj(text))}
                placeholder="000.000.000-00"
                keyboardType="numeric"
                maxLength={18}
              />
            </InputGroup>

            <SearchButton onPress={handleSearch} disabled={isLoading}>
              {isLoading ? (
                <Text size={20} color="#fff" weight="600">Consultando...</Text>
              ) : (
                <>
                  <Search size={24} color="#fff" />
                  <Text size={20} color="#fff" weight="600">Consultar Débitos</Text>
                </>
              )}
            </SearchButton>
          </SearchForm>
        </SearchSection>

        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingBottom: selectedDebts.length > 0 ? 140 : 30 }}
          showsVerticalScrollIndicator={false}
        >
          {vehicleInfo && (
            <VehicleCard>
              <VehicleInfo>
                <Car size={32} color="#3182CE" />
                <Text size={24} weight="700" color="#1A202C">
                  Informações do Veículo
                </Text>
              </VehicleInfo>

              <VehicleDetail>
                <Text size={18} color="#718096">Placa</Text>
                <Text size={20} weight="600" color="#1A202C">{vehicleInfo.plate}</Text>
              </VehicleDetail>

              <VehicleDetail>
                <Text size={18} color="#718096">RENAVAM</Text>
                <Text size={20} weight="600" color="#1A202C">{vehicleInfo.renavam}</Text>
              </VehicleDetail>

              <VehicleDetail>
                <Text size={18} color="#718096">Documento</Text>
                <Text size={20} weight="600" color="#1A202C">{vehicleInfo.document}</Text>
              </VehicleDetail>

              <VehicleDetail>
                <Text size={18} color="#718096">UF</Text>
                <Text size={20} weight="600" color="#1A202C">{vehicleInfo.uf}</Text>
              </VehicleDetail>
            </VehicleCard>
          )}

          {debts.length > 0 && (
            <DebtsContainer>
              <Text size={28} weight="700" color="#1A202C" style={{ marginBottom: 24 }}>
                Débitos Encontrados ({debts.length})
              </Text>

              {debts.map(debt => (
                <DebtItem key={debt.id} selected={selectedDebts.includes(debt.id)}>
                  <DebtHeader>
                    <DebtIcon backgroundColor={getDebtColor(debt.type)}>
                      {getDebtIcon(debt.type)}
                    </DebtIcon>

                    <DebtContent>
                      <DebtTitle>
                        <Text size={20} weight="700" color="#1A202C">
                          {debt.title}
                        </Text>
                        {debt.isExpired && (
                          <DebtBadge color="#E53E3E">
                            <Text size={14} color="#fff" weight="600">VENCIDO</Text>
                          </DebtBadge>
                        )}
                        {debt.hasDiscount && (
                          <DebtBadge color="#38A169">
                            <Text size={14} color="#fff" weight="600">DESCONTO</Text>
                          </DebtBadge>
                        )}
                      </DebtTitle>

                      <DebtMeta>
                        <Calendar size={18} color="#718096" />
                        <Text size={16} color="#718096">Vencimento: {debt.dueDate}</Text>
                      </DebtMeta>
                    </DebtContent>

                    <DebtAmount>
                      <Text size={24} weight="700" color="#1A202C">
                        {formatCurrency(debt.amount)}
                      </Text>
                    </DebtAmount>
                  </DebtHeader>

                  <SelectButton
                    onPress={() => toggleDebtSelection(debt.id)}
                    selected={selectedDebts.includes(debt.id)}
                  >
                    {selectedDebts.includes(debt.id) ? (
                      <>
                        <Check size={20} color="#fff" />
                        <Text size={18} color="#fff" weight="600">Selecionado</Text>
                      </>
                    ) : (
                      <Text size={18} color="#3182CE" weight="600">Selecionar</Text>
                    )}
                  </SelectButton>
                </DebtItem>
              ))}
            </DebtsContainer>
          )}

          {vehicleInfo && debts.length === 0 && (
            <EmptyState>
              <EmptyIcon>
                <CheckCircle2 size={64} color="#38A169" />
              </EmptyIcon>
              <Text size={32} weight="700" color="#38A169" style={{ textAlign: 'center', marginTop: 24 }}>
                Parabéns, seus débitos estão sendo liquidados!
              </Text>
              <Text size={20} color="#718096" style={{ textAlign: 'center', marginTop: 12 }}>
                Para consultar novos débitos, faça uma nova pesquisa.
              </Text>
            </EmptyState>
          )}
        </ScrollView>

        {selectedDebts.length > 0 && (
          <FloatingPayment>
            <PaymentInfo>
              <Text size={16} color="#718096">
                {selectedDebts.length} débito{selectedDebts.length > 1 ? 's' : ''} selecionado{selectedDebts.length > 1 ? 's' : ''}
              </Text>
              <Text size={26} weight="700" color="#1A202C">
                {formatCurrency(calculateTotal())}
              </Text>
            </PaymentInfo>

            <PaymentButton onPress={() => setIsPaymentModalVisible(true)}>
              <CreditCard size={24} color="#fff" />
              <Text size={20} color="#fff" weight="600">Pagar</Text>
            </PaymentButton>
          </FloatingPayment>
        )}
      </Content>

      {isLoading && (
        <LoadingOverlay>
          {/* <Backdrop /> */}
          <LoadingContent>
            <Loader size={48} color="#fff" />
            <Text size={20} color="#fff" weight="600" style={{ marginTop: 16 }}>
              {loadingMessage}
            </Text>
          </LoadingContent>
        </LoadingOverlay>
      )}

      <PaymentMethodDrawer
        visible={isPaymentModalVisible}
        onClose={() => setIsPaymentModalVisible(false)}
        onPaymentSelect={handlePaymentSelected}
        amount={convertToCents(calculateTotal())}
        service_type={ServicesTypeEnum.DEBITOS_VEICULARES}
        title="Como deseja pagar?"
        showPix={true}
        cardLabel="Cartão de Crédito"
        cancelLabel="Cancelar"
        animationType="slide"
      />
    </Container>
  );
}
