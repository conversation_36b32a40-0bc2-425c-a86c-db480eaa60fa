import React, { useEffect, useState } from 'react';
import { SafeAreaView, StatusBar } from 'react-native';
import { CustomQueryDevtools } from '../components/CustomQueryDevTools';
import GettingStartedScreen from '../components/GettingStart';
import MenuLoading from '../components/Loading/menu';
import LoginScreen from '../components/Login';
import ScreenRenderer from '../components/ScreenRenderer';
import AuthProvider from '../contexts/AuthContext';
import { defaultTheme, ThemeProvider } from '../contexts/ThemeContext';
import { useAuth } from '../hooks/useAuth';
import { CompanyConfigurationBody, useCompanyThemeConfig } from '../hooks/useCompanyThemeConfig';
import { Contract, useContract } from '../hooks/useContract';
import { useDeviceSerial } from '../hooks/useDeviceSerial';
import { useNavigation } from '../hooks/useNavigation';
import { setGlobalLogout } from '../utils/api';
import ErrorScreen from '../components/Errors';

function MainContent() {
  const { isAuthenticated, isLoading, isCheckingAuth, login, logout } = useAuth();
  const { serialNumber } = useDeviceSerial();
  const { currentScreen, navigateToScreen, navigateToMenu } = useNavigation();

  const [isGettingStarted, setIsGettingStarted] = useState(true);

  const [paymentData] = useState({
    amount: 125.5,
    qrCodeData:
      '00020126580014BR.GOV.BCB.PIX0136fcb97a59-0cfb-4ea1-8809-dd9a517e3dee520400005303986540550.005802BR5925Ian Gabriel Borges Martin6009SAO PAULO62140510gpgN4R6cMI6304FEFE',
    expirationTime: 50000,
  });

  const handleExpired = () => {
    console.log('QR Code expirado!');
  };

  const handlePaymentReceived = () => {
    console.log('Pagamento recebido!');
  };

  useEffect(() => {
    setGlobalLogout(logout);
  }, [logout]);

  const {
    data: companyThemeConfig = {} as CompanyConfigurationBody,
    isLoading: isLoadingThemeConfig,
    isError: isErrorThemeConfig,
    refetch: refetchCompanyThemeConfig,
  } = useCompanyThemeConfig({
    enabled: isAuthenticated && !isGettingStarted,
  });

  const {
    data: contract = {} as Contract,
      isLoading: isLoadingContract,
      isError: isErrorContract,
      refetch: refetchContract,
  } = useContract(serialNumber || '', {
    enabled: !!serialNumber && isAuthenticated,
  });

  if (isErrorContract || isErrorThemeConfig) {
    return (
      <ErrorScreen onRetry={() => {
        refetchContract();
        refetchCompanyThemeConfig();
      }}/>
    );
  }

  if (isCheckingAuth || isLoadingContract) {
    return (
      <SafeAreaView
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <StatusBar />
        <MenuLoading theme={companyThemeConfig.theme} />
      </SafeAreaView>
    );
  }

  return (
    <AuthProvider onLogout={logout}>
      <ThemeProvider
        themeConfig={companyThemeConfig?.theme?.accentColor ? companyThemeConfig.theme : defaultTheme}
        isLoading={isLoadingThemeConfig}
      >
        {isAuthenticated ? (

          isGettingStarted
            ?
              <GettingStartedScreen onGetStarted={() => setIsGettingStarted(false)} />
            :
              <ScreenRenderer
                currentScreen={currentScreen}
                isLoadingThemeConfig={isLoadingThemeConfig}
                companyThemeConfig={companyThemeConfig}
                contract={contract}
                onNavigateToMenu={navigateToMenu}
                onNavigateToScreen={navigateToScreen}
            />
        ) : (
          <LoginScreen onLoginSuccess={login} isLoading={isLoading} />
        )}
        <CustomQueryDevtools />
      </ThemeProvider>
    </AuthProvider>
  );
}

export function Main() {
  return (
    <>
      <StatusBar />
      <MainContent />
    </>
  );
}
