import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { IAuthResponse } from '../types/AuthResponse';
import { api } from '../utils/api';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    checkAuthToken();
  }, []);

  const checkAuthToken = async () => {
    try {
      const token = await AsyncStorage.getItem('accessToken');
      if (token) {
        api.defaults.headers.common.Authorization = `Bearer ${token}`;
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.log('Erro ao verificar token:', error);
    } finally {
      setIsCheckingAuth(false);
    }
  };

  const login = async (credentials: {
    email: string;
    password: string;
    totem_serial: string;
  }) => {
    setIsLoading(true);

    try {
      const response = await api.post('/auth/login', {
        login: credentials.email,
        password: credentials.password,
        totem_serial: credentials.totem_serial,
      });

      const responseData = response.data as IAuthResponse;
      const { token } = responseData;

      if (token) {
        await AsyncStorage.setItem('accessToken', token);
        api.defaults.headers.common.Authorization = `Bearer ${token}`;
        setIsAuthenticated(true);
      } else {
        Alert.alert('Erro', 'Token de acesso não encontrado na resposta');
      }
    } catch (error: any) {
      console.error('Erro no login:', error);

      if (error.response?.status === 400) {
        Alert.alert('Erro', JSON.stringify(error.response.data));
      } else if (error.response?.status >= 500) {
        Alert.alert('Erro', 'Erro interno do servidor. Tente novamente.');
      } else {
        Alert.alert('Erro', 'Erro ao fazer login. Verifique sua conexão.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('accessToken');
      delete api.defaults.headers.common.Authorization;
      setIsAuthenticated(false);
    } catch (error) {
      console.log('Erro ao fazer logout:', error);
    }
  };

  return {
    isAuthenticated,
    isLoading,
    isCheckingAuth,
    login,
    logout,
  };
};
