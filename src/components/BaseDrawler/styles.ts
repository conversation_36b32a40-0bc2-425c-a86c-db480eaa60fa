import { Dimensions } from 'react-native';
import styled from 'styled-components/native';

const { height } = Dimensions.get('window');

export const DrawerOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: flex-end;
`;

export const DrawerContainer = styled.View`
  background-color: #ffffff;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  min-height: ${height * 0.75}px;
  max-height: ${height * 0.95}px;
  width: 100%;
  shadow-color: #000;
  shadow-offset: 0px -4px;
  shadow-opacity: 0.25;
  shadow-radius: 16px;
  elevation: 20;
`;

export const DragIndicator = styled.View`
  width: 80px;
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  align-self: center;
  margin-top: 16px;
  margin-bottom: 8px;
`;

export const DrawerContent = styled.View`
  flex: 1;
  padding: 32px 48px 48px 48px;
`;

export const DrawerHeader = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48px;
  padding-bottom: 24px;
  border-bottom-width: 2px;
  border-bottom-color: #f1f5f9;
`;

export const DrawerTitle = styled.Text`
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  flex: 1;
  text-align: ${props => props.theme?.centered ? 'center' : 'left'};
`;

export const CloseButton = styled.TouchableOpacity`
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: #f8fafc;
  justify-content: center;
  align-items: center;
  border: 2px solid #e2e8f0;
`;

export const BackButton = styled.TouchableOpacity`
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: #f8fafc;
  justify-content: center;
  align-items: center;
  border: 2px solid #e2e8f0;
  margin-right: 24px;
`;
