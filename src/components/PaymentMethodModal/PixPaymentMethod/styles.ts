import { Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styled from 'styled-components/native';

export const Container = styled.View`
  flex: 1;
  background-color: #f0f2f5;
`;

export const Header = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const BackButton = styled.TouchableOpacity`
  position: absolute;
  left: 0;
  padding: 20px;
  border-radius: 50px;
  z-index: 1;
`;

export const Content = styled.ScrollView.attrs({
  contentContainerStyle: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48,
  },
})``;

export const PaymentCard = styled.View`
  background-color: #fff;
  border-radius: 40px;
  padding: 64px;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 12px;
  shadow-opacity: 0.1;
  shadow-radius: 24px;
  elevation: 12;
  width: 100%;
  max-width: 900px;
`;

export const AnimatedStatusContainer = styled(LinearGradient)`
  width: 200px;
  height: 200px;
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  shadow-color: #000;
  shadow-offset: 0px 8px;
  shadow-opacity: 0.2;
  shadow-radius: 16px;
  elevation: 8;
`;

export const WaitingContent = styled.View`
  align-items: center;
  width: 100%;
`;

export const SuccessContent = styled.View`
  align-items: center;
  width: 100%;
`;

interface TextProps {
  weight?: '400' | '600' | '700';
  color?: string;
  size?: number;
  opacity?: number;
}

export const InstructionText = styled(Text)<TextProps>`
  font-family: ${({ weight }) => {
    switch (weight) {
      case '700': return 'GeneralSans-Bold';
      case '600': return 'GeneralSans-SemiBold';
      default: return 'GeneralSans-Regular';
    }
  }};
  text-align: center;
  margin-bottom: 32px;
  line-height: 52px;
  color: ${({ color }) => color || '#333'};
  font-size: ${({ size }) => size ? `${size}px` : '32px'};
`;

export const AmountDisplay = styled.View`
  border-radius: 24px;
  margin-bottom: 32px;
  width: 100%;
  align-items: center;
  shadow-color: #D73035;
  shadow-offset: 0px 8px;
  shadow-opacity: 0.3;
  shadow-radius: 16px;
  elevation: 10;
`;

export const QRCodeWrapper = styled.View`
  padding: 24px;
  background-color: #fff;
  border-radius: 32px;
  margin-bottom: 32px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.1;
  shadow-radius: 8px;
  elevation: 5;
`;

export const Timer = styled.View`
  background-color: #fff;
  border: 2px solid #f0f0f0;
  border-radius: 100px;
  padding: 24px 48px;
  margin-bottom: 32px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

export const Card = styled.View`
  width: 100%;
  padding: 32px;
  background-color: #f8f9fa;
  border-radius: 20px;
  border: 1px solid #eee;
`;
