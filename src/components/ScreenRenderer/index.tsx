import React from 'react';
import { SafeAreaView } from 'react-native';
import { ScreenType } from '../../hooks/useNavigation';
import { CompanyConfigurationBody } from '../../hooks/useCompanyThemeConfig';
import { Contract } from '../../hooks/useContract';
import LoadingTicketeira from '../Loading/ticketeira';
import MenuLoading from '../Loading/menu';
import Ticketing from '../Ticketing';
import { BillPaymentScreen } from '../BillPayment/BillPaymentScreen';
import { VehicleDebtsScreen } from '../VehicleDebitPayment/VehicleDebitPaymentScreen';
import { MainMenuScreen } from '../MainMenuScreen';

interface ScreenRendererProps {
  currentScreen: ScreenType;
  isLoadingThemeConfig: boolean;
  companyThemeConfig: CompanyConfigurationBody;
  contract: Contract;
  onNavigateToMenu: () => void;
  onNavigateToScreen: (screen: ScreenType) => void;
}

export default function ScreenRenderer({
  currentScreen,
  isLoadingThemeConfig,
  companyThemeConfig,
  contract,
  onNavigateToMenu,
  onNavigateToScreen,
}: ScreenRendererProps) {
  if (isLoadingThemeConfig) {
    return (
      <SafeAreaView
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        {currentScreen !== 'menu' ? (
          <LoadingTicketeira />
        ) : (
          <MenuLoading theme={companyThemeConfig.theme} />
        )}
      </SafeAreaView>
    );
  }

  switch (currentScreen) {
    case 'ticketing':
      return <Ticketing contract={contract} onBack={onNavigateToMenu} />;

    case 'billPayment':
      return <BillPaymentScreen onBack={onNavigateToMenu} />;

    case 'vehicleDebts':
      return <VehicleDebtsScreen onBack={onNavigateToMenu} />;

    case 'menu':
    default:
      return (
        <MainMenuScreen
          onBillPaymentPress={() => onNavigateToScreen('billPayment')}
          onTicketingPress={() => onNavigateToScreen('ticketing')}
          onVehicleDebtsPress={() => onNavigateToScreen('vehicleDebts')}
          userName={companyThemeConfig?.franchise?.Company.name || 'Usuário'}
          contract={contract}
        />
      );
  }
}
