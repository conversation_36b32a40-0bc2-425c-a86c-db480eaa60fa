import { <PERSON>ert, ToastAndroid } from 'react-native';
import SunmiPrintModule from '../SunmiPrintModule';
import { formatTime } from '../../../utils/formatTime';
import { ICartItem } from '../../../types/CartItem';


interface ITicketData {
  cartItems: ICartItem[];
  establishmentName?: string;
  orderNumber?: string;
}

export class TicketPrinter {
  private static formatDate(date: Date): string {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  }

  public static async printTickets(data: ITicketData): Promise<void> {
    try {
      SunmiPrintModule.initPrinter();
      SunmiPrintModule.enterPrinterBuffer();

      const currentDate = new Date();

      // Imprimir um ticket para cada item
      for (let i = 0; i < data.cartItems.length; i++) {
        const item = data.cartItems[i];

        for (let qty = 0; qty < item.quantity; qty++) {
          // Reset para config padrao
          SunmiPrintModule.reset(null);

          // Header
          SunmiPrintModule.setAlignment(1, null);
          SunmiPrintModule.setFontSize(20, null);
          SunmiPrintModule.setBold(true, null);
          SunmiPrintModule.printText(data.establishmentName || 'ESTABELECIMENTO', null);
          SunmiPrintModule.printLine(1, null);

          // Linha separadora
          SunmiPrintModule.printText('========================', null);
          SunmiPrintModule.printLine(1, null);

          // Titulo do ticket
          SunmiPrintModule.setFontSize(18, null);
          SunmiPrintModule.printText('TICKET PRODUCAO', null);
          SunmiPrintModule.printLine(1, null);

          // numero do pedido
          if (data.orderNumber) {
            SunmiPrintModule.setFontSize(16, null);
            SunmiPrintModule.setBold(false, null);
            SunmiPrintModule.printText(`Pedido: ${data.orderNumber}`, null);
            SunmiPrintModule.printLine(1, null);
          }

          // data e hora
          SunmiPrintModule.setAlignment(0, null);
          SunmiPrintModule.setFontSize(14, null);
          SunmiPrintModule.printTable(
            ['Data:', this.formatDate(currentDate)],
            [12, 12],
            [0, 2],
            null
          );
          SunmiPrintModule.printTable(
            ['Hora:', formatTime(currentDate)],
            [12, 12],
            [0, 2],
            null
          );
          SunmiPrintModule.printLine(1, null);

          // Nome do produto
          SunmiPrintModule.setAlignment(1, null);
          SunmiPrintModule.setFontSize(20, null);
          SunmiPrintModule.setBold(true, null);
          SunmiPrintModule.printText(item.Product.name, null);
          SunmiPrintModule.printLine(1, null);

          // Quantidade unitaria
          SunmiPrintModule.setFontSize(16, null);
          SunmiPrintModule.setBold(false, null);
          SunmiPrintModule.printText('Quantidade: 1 unidade', null);
          SunmiPrintModule.printLine(1, null);

          // Descricao
          if (item.Product.description) {
            SunmiPrintModule.setAlignment(0, null);
            SunmiPrintModule.setFontSize(14, null);
            SunmiPrintModule.printText(`Descricao: ${item.Product.description}`, null);
            SunmiPrintModule.printLine(1, null);
          }

          // Ingredientes
          if (item.Product.ingredients && item.Product.ingredients.length > 0) {
            SunmiPrintModule.printText('Ingredientes:', null);
            SunmiPrintModule.printLine(1, null);

            item.Product.ingredients.forEach(ingredient => {
              SunmiPrintModule.printText(`- ${ingredient.name}`, null);
              SunmiPrintModule.printLine(1, null);
            });
          }

          SunmiPrintModule.printLine(1, null);

          // rodape
          SunmiPrintModule.setAlignment(1, null);
          SunmiPrintModule.setFontSize(12, null);
          SunmiPrintModule.printText('========================', null);
          SunmiPrintModule.printLine(1, null);
          SunmiPrintModule.printText(`Ticket ${qty + 1} de ${item.quantity}`, null);
          SunmiPrintModule.printLine(2, null);

          // cortar papel apenas se nao for o último ticket
          if (!(i === data.cartItems.length - 1 && qty === item.quantity - 1)) {
            SunmiPrintModule.cutPaper(null);
          }
        }
      }

      // Corte final
      SunmiPrintModule.cutPaper(null);

      await SunmiPrintModule.exitPrinterBuffer();

      ToastAndroid.show('Tickets impressos com sucesso!', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Erro ao imprimir tickets:', error);
      Alert.alert(
        'Erro na Impressão',
        'Não foi possível imprimir os tickets. Verifique se a impressora está conectada.',
        [{ text: 'OK' }]
      );
      throw error;
    }
  }
}

export const printProductionTickets = async (
  cartItems: ICartItem[],
  additionalData?: Partial<ITicketData>
): Promise<void> => {
  const ticketData: ITicketData = {
    cartItems,
    ...additionalData,
  };

  await TicketPrinter.printTickets(ticketData);
};
