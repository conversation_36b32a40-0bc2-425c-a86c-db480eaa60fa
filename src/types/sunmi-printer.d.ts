export interface PrintResult {
  code: {
    code: string;
    message: string;
  }
}

export interface ISunmiPrintModule {
  /**
   * Inicializa a impressora Sunmi
   * @returns Promise que resolve com mensagem de sucesso ou rejeita com erro
   */
  initPrinter(): Promise<string>;

  /**
   * Obtém o status atual da impressora
   * @param callback Callback que recebe o status da impressora (-1 para erro)
   */
  getPrinterStatus(callback: (status: number) => void): void;

  /**
   * Imprime texto simples
   * @param text Texto a ser impresso
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printText(text: string, callback: (success: boolean) => void): void;

  /**
   * Imprime imagem a partir de string Base64
   * @param data String Base64 da imagem
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printImage(data: string, callback: (success: boolean) => void): void;

  /**
   * Imprime código de barras
   * @param data Dados do código de barras
   * @param symbology Tipo de simbologia do código de barras
   * @param height Altura do código de barras
   * @param width Largura do código de barras
   * @param textPosition Posição do texto (0: sem texto, 1: acima, 2: abaixo, 3: ambos)
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printBarcode(
    data: string,
    symbology: number,
    height: number,
    width: number,
    textPosition: number,
    callback: (success: boolean) => void
  ): void;

  /**
   * Imprime QR Code
   * @param data Dados do QR Code
   * @param moduleSize Tamanho do módulo (1-16)
   * @param errorLevel Nível de correção de erro (0-3)
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printQRCode(
    data: string,
    moduleSize: number,
    errorLevel: number,
    callback: (success: boolean) => void
  ): void;

  /**
   * Imprime tabela com colunas
   * @param colsTextArr Array com o texto de cada coluna
   * @param colsWidthArr Array com a largura de cada coluna
   * @param colsAlignArr Array com o alinhamento de cada coluna (0: esquerda, 1: centro, 2: direita)
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printTable(
    colsTextArr: string[],
    colsWidthArr: number[],
    colsAlignArr: number[],
    callback: (success: boolean) => void
  ): void;

  /**
   * Imprime linhas em branco
   * @param line Número de linhas a serem impressas
   * @param callback Callback que recebe true/false indicando sucesso
   */
  printLine(line: number, callback: (success: boolean) => void): void;

  cutPaper(callback: (success: boolean) => void): void;

  /**
   * Reseta a impressora para configurações padrão
   * @param callback Callback que recebe true/false indicando sucesso
   */
  reset(callback: (success: boolean) => void): void;

  /**
   * Define se o texto deve ser impresso em negrito
   * @param bold true para negrito, false para texto normal
   * @param callback Callback que recebe true/false indicando sucesso
   */
  setBold(bold: boolean, callback: (success: boolean) => void): void;

  /**
   * Define a altura da linha
   * @param height Altura da linha
   * @param callback Callback que recebe true/false indicando sucesso
   */
  setHeight(height: number, callback: (success: boolean) => void): void;

  /**
   * Define o tamanho da fonte
   * @param size Tamanho da fonte
   * @param callback Callback que recebe true/false indicando sucesso
   */
  setFontSize(size: number, callback: (success: boolean) => void): void;

  /**
   * Define o alinhamento do texto
   * @param alignment Alinhamento (0: esquerda, 1: centro, 2: direita)
   * @param callback Callback que recebe true/false indicando sucesso
   */
  setAlignment(alignment: number, callback: (success: boolean) => void): void;

  /**
   * Entra no modo buffer da impressora
   * Comandos subsequentes serão armazenados em buffer até exitPrinterBuffer ser chamado
   */
  enterPrinterBuffer(): void;

  /**
   * Sai do modo buffer e executa todos os comandos armazenados
   * @returns Promise que resolve com resultado da impressão
   */
  exitPrinterBuffer(): Promise<PrintResult>;
}

// Constantes úteis
export const PrinterAlignment = {
  LEFT: 0,
  CENTER: 1,
  RIGHT: 2,
} as const;

export const BarcodeTextPosition = {
  NO_TEXT: 0,
  ABOVE: 1,
  BELOW: 2,
  BOTH: 3,
} as const;

export const QRCodeErrorLevel = {
  L: 0, // ~7%
  M: 1, // ~15%
  Q: 2, // ~25%
  H: 3, // ~30%
} as const;

// Tipos auxiliares
export type PrinterAlignmentType = typeof PrinterAlignment[keyof typeof PrinterAlignment];
export type BarcodeTextPositionType = typeof BarcodeTextPosition[keyof typeof BarcodeTextPosition];
export type QRCodeErrorLevelType = typeof QRCodeErrorLevel[keyof typeof QRCodeErrorLevel];

// Tipagem para o módulo nativo
declare const SunmiPrintModule: SunmiPrintModule;
export default SunmiPrintModule;
