import { useQuery } from '@tanstack/react-query';
import { api } from '../utils/api';


export interface CompanyTheme {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  accentColor: string;
  textColor: string;
}

interface Franchise {
  id: string
  Company: Company
}

interface Company {
  name: string
  email: string
  cnpj: string
}

export interface CompanyConfigurationBody {
  id: string
  created_at: string
  updated_at: string
  logo_url: string
  theme: CompanyTheme
  brand_name: string
  franchisor_id: string
  franchise: Franchise
}

export function useCompanyThemeConfig(options = {}) {
  return useQuery({
    queryKey: ['companyConfig'],
    queryFn: async (): Promise<CompanyConfigurationBody> => {
      const { data } = await api.get('/franchisor-configuration/me');
      return data;
    },
    refetchOnWindowFocus: true,
    ...options,
  });
}
