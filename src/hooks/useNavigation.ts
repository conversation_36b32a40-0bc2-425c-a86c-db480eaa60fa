import { useState } from 'react';

export type ScreenType = 'menu' | 'ticketing' | 'billPayment' | 'vehicleDebts';

export const useNavigation = () => {
  const [currentScreen, setCurrentScreen] = useState<ScreenType>('menu');

  const navigateToScreen = (screen: ScreenType) => {
    setCurrentScreen(screen);
  };

  const navigateToMenu = () => {
    setCurrentScreen('menu');
  };

  return {
    currentScreen,
    navigateToScreen,
    navigateToMenu,
  };
};
