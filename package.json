{"name": "mobileNativo", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "axios": "^1.9.0", "concurrently": "^9.2.0", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.513.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.27.2", "react-native-linear-gradient": "^2.8.3", "react-native-qrcode-svg": "^6.3.15", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.12.0", "styled-components": "^6.1.18"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "@types/styled-components-react-native": "^5.2.5", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}