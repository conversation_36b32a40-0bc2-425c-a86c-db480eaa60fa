import React, { createContext, useContext, ReactNode } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components/native';

export interface ThemeColors {
  textColor: string;
  accentColor: string;
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
}

export const defaultTheme: ThemeColors = {
  textColor: '#212529',
  accentColor: '#17a2b8',
  primaryColor: '#007bff',
  secondaryColor: '#6c757d',
  backgroundColor: '#ffffff',
};

declare module 'styled-components/native' {
  export interface DefaultTheme extends ThemeColors {}
}

interface ThemeContextProps {
  theme: ThemeColors;
  isThemeLoaded: boolean;
}

const ThemeContext = createContext<ThemeContextProps>({
  theme: defaultTheme,
  isThemeLoaded: false,
});

interface ThemeProviderProps {
  children: ReactNode;
  themeConfig?: ThemeColors;
  isLoading?: boolean;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  themeConfig,
  isLoading = false,
}) => {
  const theme = themeConfig || defaultTheme;
  const isThemeLoaded = !isLoading && !!themeConfig;

  return (
    <ThemeContext.Provider value={{ theme, isThemeLoaded }}>
      <StyledThemeProvider theme={theme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme deve ser usado dentro de um ThemeProvider');
  }
  return context;
};
