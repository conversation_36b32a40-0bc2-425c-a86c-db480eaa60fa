<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> básica<PERSON> -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    
    <!-- <PERSON> permissão para Android 15 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
    
    <!-- Per<PERSON><PERSON><PERSON><PERSON> - <PERSON> para Android 12+ -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" 
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    
    <!-- Permissões Bluetooth - Antigas para compatibilidade -->
    <uses-permission android:name="android.permission.BLUETOOTH" 
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" 
        android:maxSdkVersion="30" />
    
    <!-- Permissões de Armazenamento -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />
    
    <!-- Novas permissões de mídia para Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    
    <!-- Permissões USB -->
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-feature android:name="android.hardware.usb.host" />
    
    <!-- Device -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    
    <!-- Permissões Sunmi -->
    <uses-permission android:name="com.sunmi.perm.MSR" />
    <uses-permission android:name="com.sunmi.perm.ICC" />
    <uses-permission android:name="com.sunmi.perm.LED" />
    <uses-permission android:name="com.sunmi.perm.SERIAL" />
    <uses-permission android:name="com.sunmi.perm.IDCard" />
    <uses-permission android:name="com.sunmi.perm.PINPAD" />
    <uses-permission android:name="com.sunmi.perm.PRINTER" />
    <uses-permission android:name="com.sunmi.perm.MONEYBOX" />
    <uses-permission android:name="com.sunmi.perm.SECURITY" />
    <uses-permission android:name="com.sunmi.perm.FINGERPRINT" />
    <uses-permission android:name="com.sunmi.perm.CONTACTLESS_CARD" />
    <uses-permission android:name="com.sunmi.perm.CUSTOMER_DISPLAY" />

    <!-- Permissões para notificações -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Para usar com dispositivos próximos -->
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" 
        android:usesPermissionFlags="neverForLocation" />

    <queries>
        <package android:name="br.com.aditum.smartpostef" />
    </queries>

    <application 
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true">
        
        <activity 
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            android:exported="true">
            
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            
            <intent-filter>
                <action android:name="com.android.example.USB_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>
            
        </activity>
        
        <service 
            android:name="woyou.aidlservice.jiuiv5.IWoyouService"
            android:enabled="true"
            android:exported="true" 
            android:foregroundServiceType="connectedDevice|specialUse">
            
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                      android:value="payment_processing" />
        </service>
        
    </application>
</manifest>