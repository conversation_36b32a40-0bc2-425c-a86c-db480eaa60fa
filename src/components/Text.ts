import styled from 'styled-components/native';

interface TextProps {
  weight?: '400' | '600' | '700';
  color?: string;
  size?: number;
  opacity?: number;
}

export const Text = styled.Text<TextProps>`
  font-family: ${({ weight }) => {
    switch (weight) {
      case '700':
        return 'GeneralSans-Bold';
      case '600':
        return 'GeneralSans-SemiBold';
      case '400':
      default:
        return 'GeneralSans-Regular';
    }
  }};
  color: ${({ color }) => color || '#333'};
  font-size: ${({ size }) => size ? `${size}px` : '16px'};
  opacity: ${({ opacity }) => opacity || 1};
`;
