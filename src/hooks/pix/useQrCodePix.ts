import { useMutation } from '@tanstack/react-query';
import { api } from '../../utils/api';

type GenerateQrCodePixData = {
  transactionId: string;
};

type QrCodePixResponse = {
  copiaCola: string;
};

export function useQrCodePix() {
  return useMutation<QrCodePixResponse, Error, GenerateQrCodePixData>({
    mutationFn: async (data) => {
      const response = await api.post<QrCodePixResponse>(
        `/transaction/get-pix-qr-code/${data.transactionId}`,
        data
      );
      return response.data;
    },
  });
}
