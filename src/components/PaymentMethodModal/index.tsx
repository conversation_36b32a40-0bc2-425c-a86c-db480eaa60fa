import React, { useState, useRef, useEffect } from 'react';
import { Animated, Dimensions, View } from 'react-native';
import type { ModalProps } from 'react-native';
import type { ServicesTypeEnum } from '../../types/enums/services-type.enum';
import { BaseDrawer } from '../BaseDrawler';
import { InstallmentOption, InstallmentsContent } from './Content/installments';
import { PaymentMethod, PaymentMethodContent } from './Content/paymentMethod';

interface PaymentMethodDrawerProps {
  visible?: boolean;
  onClose?: () => void;
  onPaymentSelect?: (method: PaymentMethod, installment?: InstallmentOption) => Promise<void> | void;
  amount: number;
  service_type: ServicesTypeEnum;
  title?: string;
  showPix?: boolean;
  showCard?: boolean;
  pixLabel?: string;
  cardLabel?: string;
  pixSubtitle?: string;
  cardSubtitle?: string;
  cancelLabel?: string;
  transparent?: boolean;
  animationType?: ModalProps['animationType'];
}

const { width: screenWidth } = Dimensions.get('window');

export function PaymentMethodDrawer({
  visible = false,
  onClose,
  onPaymentSelect,
  amount,
  service_type,

  title = 'Escolha o método de pagamento',
  showPix = true,
  showCard = true,
  pixLabel = 'PIX',
  cardLabel = 'Cartão de Crédito/Débito',
  pixSubtitle = 'Pagamento instantâneo via QR Code',
  cardSubtitle = 'Parcelamento disponível',
  cancelLabel = 'Cancelar',
  transparent = true,
  animationType = 'slide',
}: PaymentMethodDrawerProps) {
  const [showInstallments, setShowInstallments] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  const paymentMethodPosition = useRef(new Animated.Value(0)).current;
  const installmentsPosition = useRef(new Animated.Value(screenWidth)).current;

  const animateToInstallments = () => {
    setIsAnimating(true);

    Animated.parallel([
      Animated.timing(paymentMethodPosition, {
        toValue: -screenWidth,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(installmentsPosition, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowInstallments(true);
      setIsAnimating(false);
    });
  };

  const animateToPaymentMethod = () => {
    setIsAnimating(true);

    Animated.parallel([
      Animated.timing(paymentMethodPosition, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(installmentsPosition, {
        toValue: screenWidth,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowInstallments(false);
      setIsAnimating(false);
    });
  };

  useEffect(() => {
    if (!visible) {
      paymentMethodPosition.setValue(0);
      installmentsPosition.setValue(screenWidth);
      setShowInstallments(false);
      setIsAnimating(false);
    }
  }, [visible]);

  const handlePaymentSelect = (method: PaymentMethod) => {
    if (isAnimating) {return;}

    if (method === 'PIX') {
      onPaymentSelect?.(method, {
        installment_qty: 0,
        total_amount: amount,
      });
      onClose?.();
    } else if (method === 'CARD') {
      animateToInstallments();
    }
  };

  const handleInstallmentSelect = (installment: InstallmentOption) => {
    if (isAnimating) {return;}

    onPaymentSelect?.('CARD', installment);
    // setShowInstallments(false);
    paymentMethodPosition.setValue(0);
    installmentsPosition.setValue(screenWidth);
    // onClose?.();
  };

  const handleInstallmentsBack = () => {
    if (isAnimating) {return;}
    animateToPaymentMethod();
  };

  const handleCancel = () => {
    if (isAnimating) {return;}

    if (showInstallments) {
      animateToPaymentMethod();
    } else {
      onClose?.();
    }
  };

  const currentTitle = showInstallments
    ? 'Escolha o número de parcelas'
    : title;

  return (
    <BaseDrawer
      visible={visible}
      onClose={onClose}
      onBack={showInstallments ? handleInstallmentsBack : undefined}
      title={currentTitle}
      transparent={transparent}
      animationType={'slide'}
      showBackButton={false}
      showCloseButton={false}
    >
      <View style={{ flex: 1, overflow: 'hidden' }}>
        <Animated.View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            transform: [{ translateX: paymentMethodPosition }],
          }}
        >
          <PaymentMethodContent
            onPaymentSelect={handlePaymentSelect}
            onCancel={handleCancel}
            showPix={showPix}
            showCard={showCard}
            pixLabel={pixLabel}
            cardLabel={cardLabel}
            pixSubtitle={pixSubtitle}
            cardSubtitle={cardSubtitle}
            cancelLabel={cancelLabel}
          />
        </Animated.View>

        <Animated.View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            transform: [{ translateX: installmentsPosition }],
          }}
        >
          <InstallmentsContent
            onInstallmentSelect={handleInstallmentSelect}
            onCancel={handleCancel}
            amount={amount}
            service_type={service_type}
            cancelLabel={cancelLabel}
            visible={true}
          />
        </Animated.View>
      </View>
    </BaseDrawer>
  );
}

export type { PaymentMethod, InstallmentOption };
