export interface ITicketeiraProduct {
  id: string;
  quantity: number;
}

export interface ICreateTransactionRequest {
  amount: number
  installment_qty: number
  deviceSerial: string
  products?: ITicketeiraProduct[]
  billCart?: {
    id: string;
  }[];
  service_type: 'DEBITOS_VEICULARES' | 'BOLETO' | 'TICKETEIRA'
  payment_method: 'PIX' | 'CARD';
  payment_status: 'PENDING' | 'PROCESSING' | 'PAID' | 'FAILED' | 'CANCELLED';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  payment_reference?: string;
}

export interface ITransactionResponse {
  transaction: {
    id: string;
    type: string;
    company_id: string;
    totem_id: string;
    amount: string;
    total_amount: string;
    payment_method: string;
    payment_status: string;
    payment_reference: string | null;
    status: string;
    created_at: string;
    completed_at: string | null;
    updated_at: string;
  };
  transactionFee: {
    id: string;
    created_at: string;
    updated_at: string;
    base_amount: string;
    customer_fee_rate: string;
    franchise_share_rate: string;
    processing_cost_rate: string;
    customer_fee_amount: string;
    franchise_share_amount: string;
    franchisor_gain_amount: string;
    provider_cost_amount: string;
    total_amount: string;
    installment_qty: number;
    franchise_id: string;
    franchisor_id: string;
    franchise_fee_contract_id: string;
    provider_cost_contract_id: string;
    transaction_id: string;
  };
  feeCalculation: {
    base_amount: number;
    total_amount: number;
    customer_fee_amount: number;
    franchise_share_amount: number;
    franchisor_gain_amount: number;
    provider_cost_amount: number;
  };
}

export interface IUpdateTransactionStatusRequest {
  products: ITicketeiraProduct[];
  payment_status: ICreateTransactionRequest['payment_status'];
  status: ICreateTransactionRequest['status'];
  payment_reference?: string;
}
