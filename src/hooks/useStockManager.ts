import { useEffect, useMemo, useState } from 'react';
import { ICartItem } from '../types/CartItem';
import { Product } from '../types/Product';

export const useStockManager = (apiCatalog: Product[], cartItems: ICartItem[]) => {
  const [originalStock, setOriginalStock] = useState<Product[]>([]);

  useEffect(() => {
    if (apiCatalog.length > 0) {
      setOriginalStock(apiCatalog);
    }
  }, [apiCatalog]);

  const catalogWithAvailableStock = useMemo(() => {
    return originalStock.map(item => {
      const cartItem = cartItems.find(
        cartItem => cartItem.Product.id === item.Product.id
      );

      const reservedQuantity = cartItem ? cartItem.quantity : 0;
      const availableQuantity = Math.max(0, item.quantity - reservedQuantity);

      return {
        ...item,
        quantity: availableQuantity,
      };
    });
  }, [originalStock, cartItems]);

  const hasStock = (productId: string, requestedQuantity: number = 1): boolean => {
    const item = catalogWithAvailableStock.find(
      item => item.Product.id === productId
    );
    return item ? item.quantity >= requestedQuantity : false;
  };

  const getAvailableStock = (productId: string): number => {
    const item = catalogWithAvailableStock.find(
      item => item.Product.id === productId
    );
    return item ? item.quantity : 0;
  };

  return {
    catalog: catalogWithAvailableStock,
    originalStock,
    hasStock,
    getAvailableStock,
  };
};
