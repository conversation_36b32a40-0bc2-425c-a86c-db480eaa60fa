export interface IAuthResponse {
  token: string;
  account: {
    id: string;
    name: string;
    email: string;
    password: string;
    cpf: string;
    role: string;
    phone: string | null;
    created_at: string;
    updated_at: string;
    Companies: {
      id: string;
      created_at: string;
      updated_at: string;
      name: string;
      email: string;
      cnpj: string;
      nature: string;
      is_active: boolean;
      Franchise: {
        id: string;
      } | null;
      Franchisor: null;
    }[];
  };
}
