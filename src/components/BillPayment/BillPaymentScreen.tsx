import {
  ArrowLeft,
  Plus,
  Shopping<PERSON>art,
  Trash2,
  X,
} from 'lucide-react-native';
import React, { useState } from 'react';
import { ActivityIndicator, <PERSON><PERSON>, <PERSON><PERSON>View, View } from 'react-native';
import { useAppTheme } from '../../contexts/ThemeContext';
import { transactionService } from '../../services/transaction/transactionService';
import { convertToReais } from '../../utils/convertToReais';
import { formatCurrency } from '../../utils/formatCurrency';
import { Button } from '../Button';
import { CardPaymentScreen } from '../PaymentMethodModal/CardPaymentMethod';
import { PaymentMethodModal } from '../PaymentMethodModal';
import { PixPaymentScreen } from '../PaymentMethodModal/PixPaymentMethod';
import { Text } from '../Text';
import {
  BackButton,
  BarcodeInput,
  BillCartCard,
  BillCartDate,
  BillCartInfo,
  BillCartRecipient,
  BillCartValue,
  BillDescription,
  BillDetailsCard,
  BillInfo,
  BillRecipient,
  BillValue,
  CartBadge,
  CartBadgeText,
  CartButton,
  CartModal,
  CloseButton,
  Container,
  Content,
  EmptyCartText,
  GradientHeader,
  HeaderSubtitle,
  HeaderTitle,
  HeaderTitleContainer,
  InputContainer,
  LoadingContainer,
  LoadingText,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  ModalTitle,
  RemoveButton,
  ScanSection,
  ScanTitle,
  TotalSection,
  TotalText,
  TotalValue,
} from './styles';
import { InstallmentType } from '../../types/aditumSdk.types';
import { InstallmentOption } from '../PaymentMethodModal/InstallmentsModal';
import { ServicesTypeEnum } from '../../types/enums/services-type.enum';

interface BillPaymentScreenProps {
  onBack: () => void;
}

interface BillDetails {
  id: string;
  barcode: string;
  recipient: string;
  value: number;
  dueDate: string;
  description?: string;
}

type PaymentMethod = 'PIX' | 'CARD';
type ScreenState = 'bill-scan' | 'pix-payment' | 'card-payment';

export function BillPaymentScreen({onBack}: BillPaymentScreenProps) {
  const [barcode, setBarcode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentBillDetails, setCurrentBillDetails] = useState<BillDetails | null>(null);
  const [billCart, setBillCart] = useState<BillDetails[]>([]);
  const [isPaymentMethodModalOpen, setIsPaymentMethodModalOpen] = useState(false);
  const [showCart, setShowCart] = useState(false);
  const [currentScreen, setCurrentScreen] = useState<ScreenState>('bill-scan');
  const [transactionId, setTransactionId] = useState<string>('');

  const [installments, setInstallments] = useState<InstallmentOption['installment_qty']>(0);
  const [cartTotalWithFees, setCartTotalWithFees] = useState(0);

  const { theme } = useAppTheme();

  const handleBarcodeChange = (text: string) => {
    const cleanedText = text.replace(/\D/g, '');
    setBarcode(cleanedText);
  };

  const handleSearch = () => {
    if (barcode.length < 10) {
      Alert.alert('Erro', 'Código de barras inválido');
      return;
    }

    const existingBill = billCart.find(bill => bill.barcode === barcode);
    if (existingBill) {
      Alert.alert('Aviso', 'Este boleto já foi adicionado ao carrinho');
      return;
    }

    setIsLoading(true);

    setTimeout(() => {
      const newBill: BillDetails = {
        id: Date.now().toString(),
        barcode: barcode,
        recipient: 'Companhia de Energia Elétrica',
        // value: Math.floor(Math.random() * 20000 + 5000),
        value: 10000,
        dueDate: '25/06/2023',
        description: 'Fatura de energia - Maio/2023',
      };

      setCurrentBillDetails(newBill);
      setIsLoading(false);
    }, 1500);
  };

  const addToCart = () => {
    if (!currentBillDetails) {
      return;
    }

    setBillCart(prev => [...prev, currentBillDetails]);
    setCurrentBillDetails(null);
    setBarcode('');
    Alert.alert('Sucesso', 'Boleto adicionado ao carrinho!');
  };

  const removeFromCart = (billId: string) => {
    setBillCart(prev => prev.filter(bill => bill.id !== billId));
  };

  const getTotalCartValue = () => {
    return billCart.reduce((total, bill) => total + bill.value, 0);
  };

  const handlePayAllBills = () => {
    if (billCart.length === 0) {
      return;
    }
    setIsPaymentMethodModalOpen(true);
  };

  const processPayment = async (paymentMethod: PaymentMethod, installment: InstallmentOption | undefined) => {
    setIsPaymentMethodModalOpen(false);
    setInstallments(installment!.installment_qty);
    setIsLoading(true);

    try {
      const res = await transactionService.createBoletosTransaction(
        getTotalCartValue(),
        installment!.installment_qty,
        paymentMethod,
        'referencia Boleto Payment',
        'PENDING',
        'PENDING',
      );

      setTransactionId(res.transaction.id);

      setCartTotalWithFees(parseFloat(res.transaction.total_amount));

     if (paymentMethod === 'CARD') {
        setCurrentScreen('card-payment');
      } else {
        throw new Error('Payment method not supported');
      }
    } catch (err: any) {
      Alert.alert(
        'Erro',
        err.message ? err.message : 'Não foi possível realizar a transação',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = async () => {
    try {
      setIsLoading(true);
      await transactionService.updateTransctionStatus(
        transactionId,
        'PAID',
        'COMPLETED',
      );

      setIsLoading(false);
      setBillCart([]);
      setShowCart(false);

      Alert.alert('Sucesso', 'Pagamento realizado com sucesso!');
    } catch (err: any) {
      Alert.alert(
        'Erro',
        err.message ? err.message : 'Não foi possível completar a transação',
      );
    }
  };

  if (currentScreen === 'card-payment') {
    return (
      <CardPaymentScreen
        onBack={() => {setCurrentScreen('bill-scan');}}
        onPaymentSuccess={handlePaymentSuccess}
        amount={cartTotalWithFees}
        installments_qty={installments}
        transactionId={transactionId}
      />
    );
  }

  return (
    <Container>
      <GradientHeader colors={['', '']}>
        <BackButton onPress={onBack}>
          <ArrowLeft size={56} color={theme.textColor} strokeWidth={2.5} />
        </BackButton>

        <HeaderTitleContainer>
          <HeaderTitle>Pagamento de Boletos</HeaderTitle>
          <HeaderSubtitle>Verifique suas dívidas em segundos</HeaderSubtitle>
        </HeaderTitleContainer>

        <CartButton onPress={() => setShowCart(true)}>
          <ShoppingCart size={56} color={theme.textColor} strokeWidth={2.5} />
          {billCart.length > 0 && (
            <CartBadge>
              <CartBadgeText>{billCart.length}</CartBadgeText>
            </CartBadge>
          )}
        </CartButton>
      </GradientHeader>

      <ScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        <Content>
          <ScanSection>
            <ScanTitle>Digite ou escaneie o código de barras</ScanTitle>

            <InputContainer>
              <BarcodeInput
                value={barcode}
                onChangeText={handleBarcodeChange}
                placeholder="Digite o código de barras"
                placeholderTextColor={theme.textColor + '80'}
                keyboardType="numeric"
                maxLength={48}
              />
            </InputContainer>

            <Button
              variant="gradient"
              gradientColors={[theme.primaryColor, `${theme.primaryColor}E6`]}
              onPress={handleSearch}
              borderRadius={18}
              minHeight={80}
              elevation={3}
              >
              <>
                <Text weight="600" color="#fff" size={24}>
                  Consultar Boleto
                </Text>
              </>
            </Button>
          </ScanSection>

          {isLoading && (
            <LoadingContainer>
              <ActivityIndicator size="large" color={theme.primaryColor} />
              <LoadingText>Consultando boleto...</LoadingText>
            </LoadingContainer>
          )}

          {currentBillDetails && !isLoading && (
            <BillDetailsCard>
              <BillRecipient>{currentBillDetails.recipient}</BillRecipient>
              <BillDescription>
                {currentBillDetails.description}
              </BillDescription>
              <BillValue>
                {formatCurrency(convertToReais(currentBillDetails.value))}
              </BillValue>
              <BillInfo>Vencimento: {currentBillDetails.dueDate}</BillInfo>
              <BillInfo>Código: {currentBillDetails.barcode}</BillInfo>

              <Button
                variant="gradient"
                gradientColors={['#22c55e', '#16a34a']}
                onPress={addToCart}
                borderRadius={18}
                minHeight={80}
                elevation={2}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Plus size={20} color="#fff" />
                  <Text weight="600" color="#fff" style={{ marginLeft: 15, fontSize: 24 }}>
                    Adicionar ao Carrinho
                  </Text>
                </View>
              </Button>
            </BillDetailsCard>
          )}
        </Content>
      </ScrollView>

      <CartModal
        visible={showCart}
        animationType="fade"
        transparent
        onRequestClose={() => setShowCart(false)}>
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Carrinho de Boletos</ModalTitle>
              <CloseButton onPress={() => setShowCart(false)}>
                <X size={20} color={theme.textColor} />
              </CloseButton>
            </ModalHeader>

            {billCart.length === 0 ? (
              <EmptyCartText>Nenhum boleto adicionado</EmptyCartText>
            ) : (
              <ScrollView
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}>
                {billCart.map(bill => (
                  <BillCartCard key={bill.id}>
                    <BillCartInfo>
                      <BillCartRecipient>{bill.recipient}</BillCartRecipient>
                      <BillCartValue>
                        {formatCurrency(convertToReais(bill.value))}
                      </BillCartValue>
                      <BillCartDate>Venc: {bill.dueDate}</BillCartDate>
                    </BillCartInfo>
                    <RemoveButton onPress={() => removeFromCart(bill.id)}>
                      <Trash2 size={18} color="#ef4444" />
                    </RemoveButton>
                  </BillCartCard>
                ))}

                <TotalSection>
                  <TotalText>Total a pagar:</TotalText>
                  <TotalValue>
                    {formatCurrency(convertToReais(getTotalCartValue()))}
                  </TotalValue>
                </TotalSection>

                <Button
                  variant="gradient"
                  gradientColors={['#22c55e', '#16a34a']}
                  onPress={handlePayAllBills}
                  borderRadius={18}
                  minHeight={100}
                >
                  <>
                    <Text weight="700" color="#fff" size={28}>
                      Pagar Todos os Boletos
                    </Text>
                  </>
                </Button>
              </ScrollView>
            )}
          </ModalContent>
        </ModalOverlay>
      </CartModal>

      <PaymentMethodModal
        visible={isPaymentMethodModalOpen}
        onClose={() => setIsPaymentMethodModalOpen(false)}
        onPaymentSelect={processPayment}
        amount={getTotalCartValue()}
        showPix={false}
        service_type={ServicesTypeEnum.BOLETO}
        title="Selecione como pagar"
        cardLabel="Cartão de Crédito"
        cancelLabel="Fechar"
        animationType="slide"
      />
    </Container>
  );
}
