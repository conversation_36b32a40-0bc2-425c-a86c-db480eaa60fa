import { Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styled from 'styled-components/native';
const { width, height } = Dimensions.get('window');

export const Container = styled.View`
  flex: 1;
  background-color: ${props => props.theme.backgroundColor};
`;

export const GradientHeader = styled(LinearGradient).attrs(props => ({
  colors: [props.theme.primaryColor, `${props.theme.primaryColor}E6`],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 1 },
}))`
  padding: 48px;
  padding-top: 80px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-height: 160px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 6px;
  elevation: 4;
`;

export const HeaderRow = styled.View`
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const BackButton = styled.TouchableOpacity`
  margin-right: 32px;
  padding: 20px;
  min-width: 96px;
  min-height: 96px;
  justify-content: center;
  align-items: center;
  border-radius: 48px;
  background-color: rgba(255, 255, 255, 0.1);
`;

export const HeaderTitleContainer = styled.View`
  flex: 1;
  align-items: center;
`;

export const HeaderTitle = styled.Text`
  color: ${props => props.theme.textColor};
  font-size: 42px;
  font-weight: 700;
  text-align: center;
`;

export const HeaderSubtitle = styled.Text`
  color: ${props => props.theme.textColor}CC;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
`;

export const CartButton = styled.TouchableOpacity`
  padding: 20px;
  min-width: 96px;
  min-height: 96px;
  border-radius: 48px;
  background-color: rgba(255, 255, 255, 0.2);
  align-items: center;
  justify-content: center;
  position: relative;
`;

export const CartBadge = styled.View`
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: ${props => props.theme.accentColor};
  align-items: center;
  justify-content: center;
`;

export const CartBadgeText = styled.Text`
  color: ${props => props.theme.textColor};
  font-size: 12px;
  font-weight: 600;
`;

export const Content = styled.View`
  flex: 1;
  padding: 40px;
`;

export const ScanSection = styled.View`
  background-color: ${props => props.theme.secondaryColor};
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 35px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.12;
  shadow-radius: 16px;
  elevation: 8;
`;

export const ScanTitle = styled.Text`
  font-size: 48px;
  font-weight: 600;
  color: ${props => props.theme.textColor};
  margin-bottom: 25px;
  text-align: center;
`;

export const InputContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${props => props.theme.backgroundColor};
  border-radius: 18px;
  padding: 8px;
  margin-bottom: 25px;
`;

export const BarcodeInput = styled.TextInput`
  flex: 1;
  padding: 22px 25px;
  font-size: 24px;
  color: ${props => props.theme.textColor};
`;

export const GradientBackground = styled(LinearGradient).attrs(props => ({
  colors: [props.theme.primaryColor, `${props.theme.primaryColor}F2`],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0 },
}))`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
`;

export const BillDetailsCard = styled.View`
  background-color: ${props => props.theme.secondaryColor};
  border-radius: 24px;
  padding: 35px;
  margin-bottom: 35px;
  border: 2px solid ${props => props.theme.primaryColor};
  shadow-color: ${props => props.theme.primaryColor};
  shadow-offset: 0px 6px;
  shadow-opacity: 0.18;
  shadow-radius: 18px;
  elevation: 10;
`;

export const BillRecipient = styled.Text`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.textColor};
  margin-bottom: 15px;
`;

export const BillDescription = styled.Text`
  font-size: 20px;
  color: ${props => props.theme.textColor}99;
  margin-bottom: 20px;
`;

export const BillValue = styled.Text`
  font-size: 36px;
  font-weight: 800;
  color: ${props => props.theme.textColor};
  margin-bottom: 15px;
`;

export const BillInfo = styled.Text`
  font-size: 20px;
  color: ${props => props.theme.textColor}CC;
  margin-bottom: 8px;
`;

export const AddToCartContainer = styled.TouchableOpacity`
  border-radius: 18px;
  margin-top: 25px;
  overflow: hidden;
`;

export const AddToCartGradient = styled(LinearGradient).attrs(props => ({
  colors: [props.theme.primaryColor, `${props.theme.primaryColor}E6`],
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0 },
}))`
  padding: 25px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
`;

export const LoadingContainer = styled.View`
  background-color: ${props => props.theme.secondaryColor};
  border-radius: 24px;
  padding: 60px;
  align-items: center;
  justify-content: center;
  margin-bottom: 35px;
`;

export const LoadingText = styled.Text`
  color: ${props => props.theme.textColor}99;
  font-size: 24px;
  margin-top: 25px;
`;

export const CartModal = styled.Modal``;

export const ModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: flex-end;
`;

export const ModalContent = styled.View`
  background-color: ${props => props.theme.secondaryColor};
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;
  padding: 40px;
  max-height: ${height * 0.85}px;
`;

export const ModalHeader = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 35px;
`;

export const ModalTitle = styled.Text`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.textColor};
`;

export const CloseButton = styled.TouchableOpacity`
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: ${props => props.theme.backgroundColor};
  align-items: center;
  justify-content: center;
`;

export const EmptyCartText = styled.Text`
  text-align: center;
  color: ${props => props.theme.textColor}99;
  font-size: 24px;
  margin: 60px 0;
`;

export const BillCartCard = styled.View`
  background-color: ${props => props.theme.backgroundColor};
  border-radius: 18px;
  padding: 25px;
  margin-bottom: 20px;
  flex-direction: row;
  align-items: center;
`;

export const BillCartInfo = styled.View`
  flex: 1;
`;

export const BillCartRecipient = styled.Text`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.textColor};
  margin-bottom: 8px;
`;

export const BillCartValue = styled.Text`
  font-size: 28px;
  font-weight: 700;
  color: ${props => props.theme.primaryColor};
  margin-bottom: 8px;
`;

export const BillCartDate = styled.Text`
  font-size: 18px;
  color: ${props => props.theme.textColor}99;
`;

export const RemoveButton = styled.TouchableOpacity`
  width: 55px;
  height: 55px;
  border-radius: 28px;
  background-color: ${props => props.theme.accentColor}33;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
`;

export const TotalSection = styled.View`
  background-color: ${props => props.theme.backgroundColor};
  border-radius: 18px;
  padding: 35px;
  margin: 35px 0;
`;

export const TotalText = styled.Text`
  font-size: 24px;
  color: ${props => props.theme.textColor}CC;
  margin-bottom: 15px;
`;

export const TotalValue = styled.Text`
  font-size: 42px;
  font-weight: 800;
  color: ${props => props.theme.primaryColor};
`;
