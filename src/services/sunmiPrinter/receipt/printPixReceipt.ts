import { Alert, ToastAndroid } from 'react-native';
import { convertToReais } from '../../../utils/convertToReais';
import { formatCurrency } from '../../../utils/formatCurrency';
import SunmiPrintModule from '../SunmiPrintModule';
import { formatTime } from '../../../utils/formatTime';
import { PaymentMethod } from '../../../components/PaymentMethodModal';

interface IReceiptData {
  amount: number;
  transactionId?: string;
  paymentMethod: PaymentMethod;
  establishmentName?: string;
  establishmentCNPJ?: string;
  establishmentEmail?: string;
}

export class ReceiptPrinter {
  private static formatDate(date: Date): string {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  }

  public static async printReceipt(data: IReceiptData): Promise<void> {
    try {
      SunmiPrintModule.initPrinter();

      SunmiPrintModule.enterPrinterBuffer();

      // Reset para config padrao
      SunmiPrintModule.reset(null);

      const currentDate = new Date();
      const formattedAmount = formatCurrency(convertToReais(data.amount));

      // Header
      SunmiPrintModule.setAlignment(1, null);
      SunmiPrintModule.setFontSize(24, null);
      SunmiPrintModule.setBold(true, null);
      SunmiPrintModule.printText(data.establishmentName || 'ESTABELECIMENTO', null);
      SunmiPrintModule.printLine(1, null);

      if (data.establishmentCNPJ) {
        SunmiPrintModule.setFontSize(16, null);
        SunmiPrintModule.setBold(false, null);
        SunmiPrintModule.printText(`CNPJ: ${data.establishmentCNPJ}`, null);
        SunmiPrintModule.printLine(1, null);
      }

      // Linha separadora
      SunmiPrintModule.setAlignment(1, null);
      SunmiPrintModule.printText('================================', null);
      SunmiPrintModule.printLine(1, null);

      // Titulo do comprovante!!
      SunmiPrintModule.setFontSize(20, null);
      SunmiPrintModule.setBold(true, null);
      SunmiPrintModule.printText(`COMPROVANTE DE PAGAMENTO VIA ${data.paymentMethod === 'CARD' ? 'CARTAO' : 'PIX'}`, null);
      SunmiPrintModule.printLine(1, null);

      SunmiPrintModule.setFontSize(18, null);
      SunmiPrintModule.setBold(false, null);
      SunmiPrintModule.printText('PAGAMENTO APROVADO', null);
      SunmiPrintModule.printLine(2, null);

      // Dados da transacaoo!!
      SunmiPrintModule.setAlignment(0, null);
      SunmiPrintModule.setFontSize(16, null);
      SunmiPrintModule.setBold(false, null);

      // Data e horario
      SunmiPrintModule.printTable(
        ['Data:', this.formatDate(currentDate)],
        [15, 15],
        [0, 2],
        null
      );

      SunmiPrintModule.printTable(
        ['Hora:', formatTime(currentDate)],
        [15, 15],
        [0, 2],
        null
      );

      SunmiPrintModule.printLine(1, null);

      // Valor
      SunmiPrintModule.setBold(true, null);
      SunmiPrintModule.setFontSize(18, null);
      SunmiPrintModule.printTable(
        ['VALOR:', formattedAmount],
        [15, 15],
        [0, 2],
        null
      );

      SunmiPrintModule.setBold(false, null);
      SunmiPrintModule.setFontSize(16, null);
      SunmiPrintModule.printLine(1, null);

      SunmiPrintModule.printTable(
        ['Metodo:', data.paymentMethod === 'CARD' ? 'CARTAO' : 'PIX'],
        [15, 15],
        [0, 2],
        null
      );

      // Id da transacao
      if (data.transactionId) {
        SunmiPrintModule.printTable(
          ['ID Transacao:', data.transactionId],
          [15, 15],
          [0, 2],
          null
        );
      }

      SunmiPrintModule.printLine(2, null);

      // Status
      SunmiPrintModule.setAlignment(1, null);
      SunmiPrintModule.setFontSize(18, null);
      SunmiPrintModule.setBold(true, null);
      SunmiPrintModule.printText('✓ TRANSACAO APROVADA', null);
      SunmiPrintModule.printLine(1, null);

      // Linha separadora
      SunmiPrintModule.setBold(false, null);
      SunmiPrintModule.setFontSize(16, null);
      SunmiPrintModule.printText('================================', null);
      SunmiPrintModule.printLine(2, null);

      // Info Adicionais
      SunmiPrintModule.setAlignment(1, null);
      SunmiPrintModule.setFontSize(14, null);
      SunmiPrintModule.printText(`Transacao realizada via ${data.paymentMethod === 'CARD' ? 'CARTAO' : 'PIX'}`, null);
      SunmiPrintModule.printLine(1, null);
      SunmiPrintModule.printText('Sistema de Pagamentos Instantaneos', null);
      SunmiPrintModule.printLine(1, null);
      SunmiPrintModule.printText('Banco Central do Brasil', null);
      SunmiPrintModule.printLine(2, null);

      // RODAPÉ
      SunmiPrintModule.setFontSize(12, null);
      SunmiPrintModule.printText('Guarde este comprovante', null);
      SunmiPrintModule.printLine(1, null);
      SunmiPrintModule.printText(`Duvidas: ${data.establishmentEmail}`, null);
      SunmiPrintModule.printLine(3, null);

      // Cortar papel
      SunmiPrintModule.cutPaper(null);

      await SunmiPrintModule.exitPrinterBuffer();

      ToastAndroid.show('Comprovante impresso com sucesso!', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Erro ao imprimir comprovante:', error);
      Alert.alert(
        'Erro na Impressão',
        'Não foi possível imprimir o comprovante. Verifique se a impressora está conectada.',
        [{ text: 'OK' }]
      );
      throw error;
    }
  }
}

export const printPaymentReceipt = async (
  amount: number,
  transactionId?: string,
  additionalData?: Partial<IReceiptData>
): Promise<void> => {
  const receiptData: IReceiptData = {
    amount,
    transactionId,
    paymentMethod: 'CARD',
    ...additionalData,
  };

  await ReceiptPrinter.printReceipt(receiptData);
};
